@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo         DLCOM Project Compilation
echo ========================================
echo.

REM Parse command line arguments
set "COMPILE_MODE="
set "SKIP_MENU="

if "%~1"=="fast" (
    set "COMPILE_MODE=fast"
    set "SKIP_MENU=true"
) else if "%~1"=="full" (
    set "COMPILE_MODE=full"
    set "SKIP_MENU=true"
) else if "%~1"=="incremental" (
    set "COMPILE_MODE=incremental"
    set "SKIP_MENU=true"
)

REM Change to project root directory
cd /d "%~dp0.."

REM Check if pom.xml exists
if not exist "pom.xml" (
    echo [ERROR] pom.xml not found in current directory!
    echo Please make sure you are in the project root directory.
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo [INFO] Project directory: %CD%
echo.

REM Show compilation mode menu if no parameter provided
if not "%SKIP_MENU%"=="true" (
    echo ========================================
    echo        Select Compilation Mode
    echo ========================================
    echo.
    echo 1. Fast Compile     - Quick compilation for development ^(~3-5 min^)
    echo    Uses: mvn compile + package without clean
    echo.
    echo 2. Full Compile     - Complete build with all checks ^(~10-15 min^)
    echo    Uses: mvn clean install -DskipTests
    echo.
    echo 3. Incremental      - Only compile changed files ^(~1-3 min^)
    echo    Uses: mvn compile -DskipTests
    echo.
    echo 4. Exit
    echo.
    set /p choice="Please select compilation mode [1-4]: "

    if "!choice!"=="1" set "COMPILE_MODE=fast"
    if "!choice!"=="2" set "COMPILE_MODE=full"
    if "!choice!"=="3" set "COMPILE_MODE=incremental"
    if "!choice!"=="4" (
        echo Operation cancelled by user.
        pause
        exit /b 0
    )

    if "!COMPILE_MODE!"=="" (
        echo [ERROR] Invalid selection. Please choose 1, 2, 3, or 4.
        pause
        exit /b 1
    )
    echo.
)

REM Get current time
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "start_time=%YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%"

echo ========================================
echo [INFO] Compilation Mode: %COMPILE_MODE%
echo [INFO] Start Time: %start_time%
echo ========================================
echo.

REM Execute compilation based on selected mode
if "%COMPILE_MODE%"=="fast" (
    echo [INFO] Fast Compile Mode - Optimized for development
    echo [INFO] This mode skips clean, documentation, and some validations
    echo.
    echo [INFO] Executing: mvn install -DskipTests -Dmaven.javadoc.skip=true -Dmaven.source.skip=true -T 1C
    echo.
    call mvn install -DskipTests -Dmaven.javadoc.skip=true -Dmaven.source.skip=true -T 1C
) else if "%COMPILE_MODE%"=="incremental" (
    echo [INFO] Incremental Compile Mode - Only changed files
    echo [INFO] This mode only compiles modified source files
    echo.
    echo [INFO] Step 1: Compiling source files...
    call mvn compile -DskipTests
    if !ERRORLEVEL! neq 0 goto :compilation_failed
    echo.
    echo [INFO] Step 2: Packaging application...
    call mvn package -DskipTests -Dmaven.compile.skip=true
) else (
    echo [INFO] Full Compile Mode - Complete build with all validations
    echo [INFO] This mode performs a complete clean build
    echo.
    echo [INFO] Executing: mvn clean install -DskipTests
    echo.
    call mvn clean install -DskipTests
)

:compilation_failed
REM Check compilation result
if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo [ERROR] Compilation FAILED!
    echo ========================================
    echo Error Code: %ERRORLEVEL%
    echo Compilation Mode: %COMPILE_MODE%
    echo.
    echo Please check the error messages above and fix the issues.
    echo.
    echo Common solutions:
    echo - Check if Java 17+ is installed and configured
    echo - Verify Maven is properly installed
    echo - Check network connection for dependency downloads
    echo - Ensure all source files are properly formatted
    echo.
    if "%COMPILE_MODE%"=="incremental" (
        echo [TIP] If incremental compilation fails, try fast or full mode:
        echo       .\bat\compile.bat fast
        echo       .\bat\compile.bat full
        echo.
    )
    if "%COMPILE_MODE%"=="fast" (
        echo [TIP] If fast compilation fails, try full mode:
        echo       .\bat\compile.bat full
        echo.
    )
    pause
    exit /b %ERRORLEVEL%
)

REM Get end time
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "end_time=%YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%"

echo.
echo ========================================
echo [SUCCESS] Compilation COMPLETED!
echo ========================================
echo Compilation Mode: %COMPILE_MODE%
echo Start Time: %start_time%
echo End Time:   %end_time%
echo.

REM Verify JAR file exists
if exist "yudao-server\target\yudao-server.jar" (
    echo [SUCCESS] JAR file created successfully
    echo Location: yudao-server\target\yudao-server.jar

    REM Get JAR file size
    for %%A in ("yudao-server\target\yudao-server.jar") do set "jar_size=%%~zA"
    set /a jar_size_mb=!jar_size!/1024/1024
    echo Size: !jar_size_mb! MB
) else (
    echo [WARNING] JAR file not found at expected location
    echo This might be normal for incremental compilation mode
)

echo.
echo ========================================
echo Next Steps:
echo ========================================
echo 1. Run .\bat\start.bat to launch the application
echo 2. Or run .\bat\build-and-start.bat for one-click start
echo.
echo Command line usage:
echo   .\bat\compile.bat fast        - Fast compilation
echo   .\bat\compile.bat full        - Full compilation
echo   .\bat\compile.bat incremental - Incremental compilation
echo ========================================

pause
