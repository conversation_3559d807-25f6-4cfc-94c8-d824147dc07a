-- ====================================
-- 不良事件分析模块数据库升级脚本
-- 版本: v2.0
-- 创建时间: 2025-02-15
-- 作者: 系统架构组
-- 功能: 优化分析模块表结构，新增智能化功能支持
-- ====================================

-- 1. 优化现有分析分配表，添加智能化字段
ALTER TABLE evt_info_assignment_analyze 
ADD COLUMN IF NOT EXISTS priority TINYINT DEFAULT 2 COMMENT '优先级：1-紧急 2-高 3-中 4-低',
ADD COLUMN IF NOT EXISTS deadline DATETIME COMMENT '截止时间',
ADD COLUMN IF NOT EXISTS auto_assigned TINYINT DEFAULT 0 COMMENT '是否自动分配：0-手动 1-自动',
ADD COLUMN IF NOT EXISTS progress_percent TINYINT DEFAULT 0 COMMENT '完成进度百分比',
ADD COLUMN IF NOT EXISTS status TINYINT DEFAULT 1 COMMENT '任务状态：1-待开始 2-进行中 3-已完成 4-已取消',
ADD COLUMN IF NOT EXISTS assigned_time DATETIME COMMENT '分配时间',
ADD COLUMN IF NOT EXISTS completed_time DATETIME COMMENT '完成时间';

-- 添加索引优化查询性能
ALTER TABLE evt_info_assignment_analyze ADD INDEX IF NOT EXISTS idx_status (status);
ALTER TABLE evt_info_assignment_analyze ADD INDEX IF NOT EXISTS idx_priority (priority);
ALTER TABLE evt_info_assignment_analyze ADD INDEX IF NOT EXISTS idx_deadline (deadline);

-- 2. 创建分析工具数据表（支持鱼骨图、5Why等工具）
CREATE TABLE IF NOT EXISTS evt_analyze_tool_data (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  event_id VARCHAR(64) NOT NULL COMMENT '事件ID',
  assignment_id VARCHAR(64) DEFAULT NULL COMMENT '分析任务ID',
  tool_type VARCHAR(50) NOT NULL COMMENT '工具类型：fishbone,five_why,pdca,fmea,rca',
  tool_name VARCHAR(100) NOT NULL COMMENT '工具名称',
  data_content LONGTEXT COMMENT '工具数据(JSON格式)',
  main_problem VARCHAR(500) COMMENT '主要问题描述',
  conclusion TEXT COMMENT '分析结论',
  creator_user_id BIGINT NOT NULL COMMENT '创建人ID',
  version INT DEFAULT 1 COMMENT '版本号',
  status TINYINT DEFAULT 1 COMMENT '是否有效：0-无效 1-有效',
  creator VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  tenant_id BIGINT NOT NULL DEFAULT 1 COMMENT '租户编号',

  INDEX idx_event_id (event_id),
  INDEX idx_assignment_id (assignment_id),
  INDEX idx_tool_type (tool_type),
  INDEX idx_creator (creator_user_id),
  INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析工具数据表';

-- 3. 创建协作参与表（支持多人协作分析）
CREATE TABLE IF NOT EXISTS evt_analyze_collaboration (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  event_id BIGINT NOT NULL COMMENT '事件ID',
  analysis_id BIGINT NOT NULL COMMENT '分析任务ID',
  user_id BIGINT NOT NULL COMMENT '参与者用户ID',
  user_name VARCHAR(100) NOT NULL COMMENT '参与者姓名',
  role_type TINYINT NOT NULL COMMENT '角色类型：1-主分析员 2-协作者 3-专家顾问 4-观察者',
  status TINYINT DEFAULT 1 COMMENT '参与状态：1-邀请中 2-已接受 3-已拒绝 4-已退出',
  invited_by BIGINT COMMENT '邀请人ID',
  invite_message VARCHAR(500) COMMENT '邀请消息',
  invite_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '邀请时间',
  response_time DATETIME COMMENT '响应时间',
  last_active_time DATETIME COMMENT '最后活跃时间',
  contribution_score INT DEFAULT 0 COMMENT '贡献度评分',
  
  INDEX idx_event_id (event_id),
  INDEX idx_analysis_id (analysis_id),
  INDEX idx_user_id (user_id),
  INDEX idx_role_type (role_type),
  INDEX idx_status (status),
  UNIQUE KEY uk_analysis_user (analysis_id, user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析协作参与表';

-- 4. 创建知识库表（支持智能推荐）
CREATE TABLE IF NOT EXISTS evt_analyze_knowledge_base (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  knowledge_type TINYINT NOT NULL COMMENT '知识类型：1-原因 2-对策 3-工具模板 4-案例',
  title VARCHAR(200) NOT NULL COMMENT '知识标题',
  content TEXT NOT NULL COMMENT '知识内容',
  summary VARCHAR(500) COMMENT '知识摘要',
  event_type VARCHAR(100) COMMENT '适用事件类型',
  tags VARCHAR(500) COMMENT '标签(逗号分隔)',
  keywords VARCHAR(300) COMMENT '关键词(用于搜索)',
  quality_score INT DEFAULT 70 COMMENT '质量评分(0-100)',
  usage_count INT DEFAULT 0 COMMENT '使用次数',
  effectiveness_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '有效性评分(0-1)',
  source VARCHAR(100) COMMENT '知识来源',
  author_id BIGINT COMMENT '作者ID',
  is_published TINYINT DEFAULT 1 COMMENT '是否发布：0-草稿 1-已发布',
  is_recommended TINYINT DEFAULT 0 COMMENT '是否推荐：0-否 1-是',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_knowledge_type (knowledge_type),
  INDEX idx_event_type (event_type),
  INDEX idx_quality_score (quality_score),
  INDEX idx_usage_count (usage_count),
  INDEX idx_is_published (is_published),
  FULLTEXT idx_content_search (title, content, summary, keywords)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析知识库表';

-- 5. 创建知识使用记录表（用于推荐算法优化）
CREATE TABLE IF NOT EXISTS evt_analyze_knowledge_usage (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  knowledge_id BIGINT NOT NULL COMMENT '知识ID',
  event_id BIGINT NOT NULL COMMENT '事件ID',
  analysis_id BIGINT NOT NULL COMMENT '分析任务ID',
  user_id BIGINT NOT NULL COMMENT '使用者ID',
  usage_type TINYINT NOT NULL COMMENT '使用类型：1-查看 2-引用 3-采纳',
  feedback_score TINYINT COMMENT '反馈评分(1-5)',
  feedback_comment VARCHAR(500) COMMENT '反馈意见',
  use_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
  
  INDEX idx_knowledge_id (knowledge_id),
  INDEX idx_event_id (event_id),
  INDEX idx_analysis_id (analysis_id),
  INDEX idx_user_id (user_id),
  INDEX idx_use_time (use_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识使用记录表';

-- 6. 初始化基础知识库数据
INSERT INTO evt_analyze_knowledge_base (knowledge_type, title, content, summary, event_type, tags, keywords, quality_score, is_recommended)
VALUES 
(1, '患者跌倒常见原因', '1. 环境因素：地面湿滑、照明不足、障碍物等\n2. 患者因素：年龄、疾病、药物副作用\n3. 护理因素：评估不足、防护措施不当', 
'跌倒事件的主要原因分析', '患者跌倒', '跌倒,原因分析,环境,患者,护理', '跌倒 原因 环境 患者 护理 评估', 85, 1),

(2, '跌倒预防改进措施', '1. 环境改善：防滑措施、充足照明、清除障碍\n2. 患者教育：跌倒风险宣教、正确使用辅助器具\n3. 护理强化：风险评估、巡视频率、安全防护',
'跌倒事件的预防和改进措施', '患者跌倒', '跌倒,预防,措施,环境,教育,护理', '跌倒 预防 措施 环境 教育 护理 评估', 88, 1),

(3, '鱼骨图分析模板', '鱼骨图分析框架：\n主干：核心问题\n分支：人员、设备、方法、材料、环境、管理六个维度\n使用方法：逐层分解，找出根本原因',
'鱼骨图(因果分析图)分析模板', '通用', '鱼骨图,因果分析,工具,模板', '鱼骨图 因果分析 工具 模板 原因', 90, 1),

(4, 'medication error案例分析', '案例：药物配置错误导致患者不良反应\n原因：核对制度执行不严、药物标识不清\n措施：强化三查七对、改进标识系统\n效果：错误率降低60%',
'药物错误典型案例及改进效果', '用药错误', '用药错误,案例,改进,效果', '用药 错误 案例 改进 效果 分析', 82, 1);

-- 7. 创建分析任务统计视图（便于数据分析）
CREATE OR REPLACE VIEW v_analyze_task_statistics AS
SELECT 
    DATE(create_time) as analysis_date,
    COUNT(*) as total_tasks,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as pending_tasks,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as in_progress_tasks,
    SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_tasks,
    SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as cancelled_tasks,
    AVG(CASE WHEN completed_time IS NOT NULL 
        THEN TIMESTAMPDIFF(HOUR, create_time, completed_time) 
        ELSE NULL END) as avg_completion_hours
FROM evt_info_assignment_analyze
WHERE create_time >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(create_time)
ORDER BY analysis_date DESC;

-- 8. 创建知识推荐评分存储过程
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS sp_calculate_knowledge_score(
    IN p_event_type VARCHAR(100),
    IN p_keywords VARCHAR(500)
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_knowledge_id BIGINT;
    DECLARE v_score DECIMAL(5,2);
    
    DECLARE cur CURSOR FOR 
        SELECT id FROM evt_analyze_knowledge_base 
        WHERE is_published = 1;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO v_knowledge_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 计算相关性评分（简化版本）
        SET v_score = (
            quality_score * 0.4 +
            (CASE WHEN event_type = p_event_type THEN 30 ELSE 0 END) +
            (CASE WHEN keywords LIKE CONCAT('%', p_keywords, '%') THEN 20 ELSE 0 END) +
            (usage_count / 10) + 
            effectiveness_score * 10
        );
        
        -- 更新评分（这里可以扩展为更复杂的推荐算法）
        UPDATE evt_analyze_knowledge_base 
        SET quality_score = LEAST(100, v_score)
        WHERE id = v_knowledge_id;
        
    END LOOP;
    
    CLOSE cur;
END //
DELIMITER ;

-- 9. 创建数据完整性检查
-- 检查是否有孤立的分析数据
SELECT '检查孤立的工具数据' as check_type, COUNT(*) as count
FROM evt_analyze_tool_data t
LEFT JOIN evt_info_assignment_analyze a ON t.analysis_id = a.id
WHERE a.id IS NULL;

-- 检查是否有孤立的协作数据  
SELECT '检查孤立的协作数据' as check_type, COUNT(*) as count
FROM evt_analyze_collaboration c
LEFT JOIN evt_info_assignment_analyze a ON c.analysis_id = a.id  
WHERE a.id IS NULL;

-- 输出升级完成信息
SELECT 
    '数据库升级完成' as status,
    NOW() as upgrade_time,
    '分析模块表结构优化完成，支持智能化功能' as description; 