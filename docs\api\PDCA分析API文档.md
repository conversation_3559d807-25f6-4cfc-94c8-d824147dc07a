# PDCA分析API文档

## 概述

PDCA分析API提供了完整的PDCA循环分析功能，支持医疗事件的Plan-Do-Check-Act分析流程。

**基础URL**: `/api/pdca`

**版本**: v1.0.0

**认证**: 需要Bearer Token认证

## API接口列表

### 1. 保存PDCA分析数据

**接口**: `POST /api/pdca/save`

**描述**: 保存或更新PDCA分析数据

**权限**: `evt:analyze:update`

**请求参数**:
```json
{
  "eventId": "string",           // 事件ID（必填）
  "analysisId": "string",        // 分析ID（可选）
  "toolType": "pdca",           // 工具类型（固定值）
  "toolName": "PDCA循环分析",    // 工具名称
  "toolData": {                 // PDCA数据（必填）
    "title": "患者跌倒事件PDCA分析",
    "plan": {
      "status": "completed",
      "responsible": "张护士",
      "deadline": "2025-08-10",
      "objectives": "减少跌倒事件发生",
      "methods": "增加巡视频次",
      "timeline": "2周内完成"
    },
    "do": {
      "status": "in_progress",
      "responsible": "李医生",
      "deadline": "2025-08-15",
      "actions": "执行巡视计划",
      "resources": "增加护理人员",
      "progress": "已完成50%"
    },
    "check": {
      "status": "pending",
      "responsible": "王主任",
      "deadline": "2025-08-20",
      "metrics": "跌倒事件发生率",
      "results": "待评估",
      "evaluation": "数据收集中"
    },
    "act": {
      "status": "pending",
      "responsible": "科室主任",
      "deadline": "2025-08-25",
      "improvements": "制定标准化流程",
      "standardization": "更新操作规程",
      "nextCycle": "下一轮改进计划"
    }
  },
  "autoSave": true,             // 是否自动保存
  "mainProblem": "患者跌倒事件", // 主要问题描述
  "conclusion": "需要加强巡视"  // 分析结论
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": "1234567890"  // 返回PDCA数据ID
}
```

### 2. 获取PDCA分析数据

**接口**: `GET /api/pdca/{eventId}`

**描述**: 获取指定事件的PDCA分析数据

**权限**: `evt:analyze:query`

**路径参数**:
- `eventId`: 事件ID

**查询参数**:
- `analysisId`: 分析ID（可选）

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": "1234567890",
    "eventId": "123",
    "analysisId": "456",
    "toolType": "pdca",
    "toolName": "PDCA循环分析",
    "toolData": {
      "title": "患者跌倒事件PDCA分析",
      "plan": {
        "status": "completed",
        "responsible": "张护士",
        "deadline": "2025-08-10",
        "objectives": "减少跌倒事件发生"
      }
    },
    "mainProblem": "患者跌倒事件",
    "conclusion": "需要加强巡视",
    "status": "draft",
    "version": 1,
    "creatorUserId": "100",
    "creatorUserName": "管理员",
    "createTime": "2025-08-03 15:30:00",
    "updateTime": "2025-08-03 15:35:00"
  }
}
```

### 3. 更新PDCA分析数据

**接口**: `PUT /api/pdca/{id}`

**描述**: 更新指定的PDCA分析数据

**权限**: `evt:analyze:update`

**路径参数**:
- `id`: PDCA数据ID

**请求参数**:
```json
{
  "toolData": {                 // PDCA数据（必填）
    "title": "更新后的标题",
    "plan": {
      "status": "completed",
      "objectives": "更新后的目标"
    }
  },
  "mainProblem": "更新后的问题描述",
  "conclusion": "更新后的结论",
  "status": "in_progress"
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": true
}
```

### 4. 删除PDCA分析数据

**接口**: `DELETE /api/pdca/{id}`

**描述**: 删除指定的PDCA分析数据（软删除）

**权限**: `evt:analyze:delete`

**路径参数**:
- `id`: PDCA数据ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": true
}
```

### 5. 完成PDCA分析

**接口**: `POST /api/pdca/{id}/complete`

**描述**: 标记PDCA分析为已完成状态

**权限**: `evt:analyze:update`

**路径参数**:
- `id`: PDCA数据ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": true
}
```

### 6. 导出PDCA分析报告

**接口**: `GET /api/pdca/{id}/export`

**描述**: 导出PDCA分析报告数据

**权限**: `evt:analyze:export`

**路径参数**:
- `id`: PDCA数据ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "title": "事件PDCA分析报告",
    "pdcaId": "1234567890",
    "eventInfo": {
      "id": "123",
      "evtname": "患者跌倒事件",
      "evtType": "跌倒"
    },
    "toolData": {
      "title": "患者跌倒事件PDCA分析",
      "plan": {...},
      "do": {...},
      "check": {...},
      "act": {...}
    },
    "mainProblem": "患者跌倒事件",
    "conclusion": "需要加强巡视",
    "exportTime": "2025-08-03T15:40:00",
    "exportUser": "100"
  }
}
```

### 7. 获取所有医疗事件模板

**接口**: `GET /api/pdca/templates`

**描述**: 获取所有可用的医疗事件PDCA模板

**权限**: `evt:analyze:query`

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": [
    {
      "eventType": "fall",
      "name": "患者跌倒事件PDCA分析",
      "description": "针对患者跌倒事件的标准化PDCA分析模板",
      "template": {
        "plan": {
          "objectives": "减少跌倒事件发生",
          "methods": "风险评估和预防措施"
        }
      }
    },
    {
      "eventType": "medication",
      "name": "用药错误事件PDCA分析",
      "description": "针对用药错误事件的标准化PDCA分析模板"
    },
    {
      "eventType": "infection",
      "name": "医院感染事件PDCA分析",
      "description": "针对医院感染事件的标准化PDCA分析模板"
    }
  ]
}
```

### 8. 获取特定类型的模板

**接口**: `GET /api/pdca/templates/{eventType}`

**描述**: 获取特定事件类型的PDCA模板

**权限**: `evt:analyze:query`

**路径参数**:
- `eventType`: 事件类型（如：fall, medication, infection）

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "eventType": "fall",
    "name": "患者跌倒事件PDCA分析",
    "description": "针对患者跌倒事件的标准化PDCA分析模板",
    "template": {
      "title": "患者跌倒事件PDCA分析",
      "plan": {
        "objectives": "减少跌倒事件发生率",
        "methods": "实施跌倒风险评估",
        "timeline": "2周内完成评估和预防措施"
      },
      "do": {
        "actions": "执行跌倒预防措施",
        "resources": "护理人员培训和设备配置"
      },
      "check": {
        "metrics": "跌倒事件发生率统计",
        "evaluation": "每月评估预防效果"
      },
      "act": {
        "improvements": "根据评估结果优化预防措施",
        "standardization": "制定标准化跌倒预防流程"
      }
    }
  }
}
```

### 9. 获取智能建议

**接口**: `GET /api/pdca/suggestions/{eventType}/{phase}`

**描述**: 获取特定事件类型和PDCA阶段的智能建议

**权限**: `evt:analyze:query`

**路径参数**:
- `eventType`: 事件类型
- `phase`: PDCA阶段（plan, do, check, act）

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": [
    "建议使用Morse跌倒风险评估量表",
    "考虑增加床边护理频次",
    "评估患者活动能力和认知状态",
    "制定个性化跌倒预防计划"
  ]
}
```

### 10. 分页查询PDCA分析数据

**接口**: `GET /api/pdca/list`

**描述**: 分页查询PDCA分析数据列表

**权限**: `evt:analyze:query`

**查询参数**:
- `pageNum`: 页码（默认1）
- `pageSize`: 页大小（默认10）
- `eventType`: 事件类型（可选）
- `status`: 状态（可选）

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "list": [
      {
        "id": "1234567890",
        "eventId": "123",
        "eventName": "患者跌倒事件",
        "eventType": "跌倒",
        "toolName": "PDCA循环分析",
        "mainProblem": "患者跌倒事件",
        "status": "in_progress",
        "progress": 75,
        "creatorUserName": "张护士",
        "createTime": "2025-08-03 15:30:00",
        "updateTime": "2025-08-03 15:35:00"
      }
    ],
    "total": 1
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### JavaScript示例

```javascript
// 保存PDCA数据
const savePDCA = async (pdcaData) => {
  const response = await fetch('/api/pdca/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify(pdcaData)
  });
  
  const result = await response.json();
  if (result.code === 0) {
    console.log('保存成功，ID:', result.data);
  } else {
    console.error('保存失败:', result.msg);
  }
};

// 获取PDCA数据
const getPDCA = async (eventId, analysisId) => {
  const url = `/api/pdca/${eventId}${analysisId ? '?analysisId=' + analysisId : ''}`;
  const response = await fetch(url, {
    headers: {
      'Authorization': 'Bearer ' + token
    }
  });
  
  const result = await response.json();
  if (result.code === 0) {
    return result.data;
  } else {
    throw new Error(result.msg);
  }
};
```

## 注意事项

1. 所有API都需要有效的认证Token
2. PDCA数据使用JSON格式存储，支持灵活的数据结构
3. 删除操作为软删除，数据不会物理删除
4. 自动保存功能建议设置30秒间隔
5. 导出功能返回的是数据对象，前端需要自行处理下载逻辑
6. 智能建议基于事件类型和知识库，可能为空
7. 模板数据包含内置模板和知识库模板
8. API响应时间目标为200ms以内
