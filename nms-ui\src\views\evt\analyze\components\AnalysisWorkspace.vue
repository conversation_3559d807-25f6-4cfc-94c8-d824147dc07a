<!--
  分析工作区组件
  
  <AUTHOR>
  @version 1.0.0
  @since 2025-02-14
-->

<template>
  <a-card 
    title="分析工作区" 
    class="analysis-workspace"
    :loading="loading"
    :tab-list="workspaceTabs"
    :active-tab-key="activeTab"
    @tab-change="handleTabChange"
  >
    <template #extra>
      <a-space>
        <a-button @click="handleSave" :loading="saving" v-if="!readonly">
          <template #icon><SaveOutlined /></template>
          保存
        </a-button>
        <a-button type="primary" @click="handleSubmit" :loading="submitting" v-if="!readonly && canSubmit">
          <template #icon><CheckOutlined /></template>
          提交分析
        </a-button>
      </a-space>
    </template>

    <!-- 事件信息标签页 -->
    <div v-show="activeTab === 'eventInfo'" class="tab-content">
      <EventInfoDisplay :event-id="eventId" />
    </div>

    <!-- 参会信息标签页 -->
    <div v-show="activeTab === 'meeting'" class="tab-content">
      <a-form layout="vertical" :model="analysisData.meetingInfo">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="会议时间">
              <a-date-picker
                :value="ensureDayjsObject(analysisData.meetingInfo.meetingTime)"
                @change="(date) => analysisData.meetingInfo.meetingTime = ensureDayjsObject(date)"
                show-time
                style="width: 100%"
                :disabled="readonly"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="会议地点">
              <a-input 
                v-model:value="analysisData.meetingInfo.location"
                placeholder="请输入会议地点"
                :disabled="readonly"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="会议主持人">
          <a-input 
            v-model:value="analysisData.meetingInfo.moderator"
            placeholder="请输入主持人姓名"
            :disabled="readonly"
          />
        </a-form-item>
        <a-form-item label="参会人员">
          <a-textarea 
            v-model:value="analysisData.meetingInfo.participants"
            :rows="3"
            placeholder="请输入参会人员信息"
            :disabled="readonly"
          />
        </a-form-item>
      </a-form>
    </div>

    <!-- 原因分析标签页 -->
    <div v-show="activeTab === 'causes'" class="tab-content">
      <div class="causes-section">
        <div class="section-header">
          <h4>原因分析</h4>
          <a-button type="dashed" @click="handleAddCause" v-if="!readonly">
            <template #icon><PlusOutlined /></template>
            添加原因
          </a-button>
        </div>
        
        <div v-if="analysisData.causes.length > 0" class="causes-list">
          <div 
            v-for="(cause, index) in analysisData.causes" 
            :key="cause.id || index"
            class="cause-item"
          >
            <a-card size="small">
              <template #title>
                <a-select 
                  v-model:value="cause.type"
                  placeholder="选择原因类型"
                  style="width: 150px"
                  :disabled="readonly"
                >
                  <a-select-option value="direct">直接原因</a-select-option>
                  <a-select-option value="root">根本原因</a-select-option>
                  <a-select-option value="contributing">促成因素</a-select-option>
                </a-select>
              </template>
              <template #extra v-if="!readonly">
                <a-button type="text" danger @click="handleRemoveCause(index)">
                  <DeleteOutlined />
                </a-button>
              </template>
              
              <a-textarea 
                v-model:value="cause.description"
                placeholder="请描述具体原因..."
                :rows="3"
                :disabled="readonly"
              />
            </a-card>
          </div>
        </div>
        
        <a-empty v-else description="暂无原因分析" />
      </div>
    </div>

    <!-- 对策措施标签页 -->
    <div v-show="activeTab === 'measures'" class="tab-content">
      <div class="measures-section">
        <div class="section-header">
          <h4>改进措施</h4>
          <a-button type="dashed" @click="handleAddMeasure" v-if="!readonly">
            <template #icon><PlusOutlined /></template>
            添加措施
          </a-button>
        </div>
        
        <div v-if="analysisData.measures.length > 0" class="measures-list">
          <div 
            v-for="(measure, index) in analysisData.measures" 
            :key="measure.id || index"
            class="measure-item"
          >
            <a-card size="small">
              <template #title>
                措施 {{ index + 1 }}
              </template>
              <template #extra v-if="!readonly">
                <a-button type="text" danger @click="handleRemoveMeasure(index)">
                  <DeleteOutlined />
                </a-button>
              </template>
              
              <a-form layout="vertical">
                <a-form-item label="措施描述">
                  <a-textarea 
                    v-model:value="measure.description"
                    placeholder="请描述具体改进措施..."
                    :rows="2"
                    :disabled="readonly"
                  />
                </a-form-item>
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item label="优先级">
                      <a-select 
                        v-model:value="measure.priority"
                        placeholder="选择优先级"
                        :disabled="readonly"
                      >
                        <a-select-option value="high">高</a-select-option>
                        <a-select-option value="medium">中</a-select-option>
                        <a-select-option value="low">低</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="责任人">
                      <a-input 
                        v-model:value="measure.responsible"
                        placeholder="责任人"
                        :disabled="readonly"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="完成期限">
                      <a-date-picker
                        :value="ensureDayjsObject(measure.deadline)"
                        @change="(date) => measure.deadline = ensureDayjsObject(date)"
                        style="width: 100%"
                        :disabled="readonly"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-card>
          </div>
        </div>
        
        <a-empty v-else description="暂无改进措施" />
      </div>
    </div>

    <!-- 原因对策关联标签页 -->
    <div v-show="activeTab === 'relations'" class="tab-content">
      <CauseMeasureRelation
        v-model:value="analysisData.relations"
        :causes="analysisData.causes"
        :measures="analysisData.measures"
        :readonly="readonly"
        @change="handleRelationsChange"
      />
    </div>

    <!-- 分析工具标签页 -->
    <div v-show="activeTab === 'tools'" class="tab-content">
      <div class="tools-section">
        <!-- 紧凑的工具选择区域 -->
        <div class="tools-selector">
          <div class="tool-card-compact"
               v-for="tool in analysisTools"
               :key="tool.key"
               @click="handleToolSelect(tool)"
               :class="{ 'selected': selectedTool === tool.key }"
          >
            <div class="tool-icon-compact">
              <component :is="tool.icon" />
            </div>
            <div class="tool-info-compact">
              <div class="tool-name-compact">{{ tool.name }}</div>
              <div class="tool-desc-compact">{{ tool.description }}</div>
            </div>
          </div>
        </div>

        <!-- 工具使用区域 -->
        <div v-if="selectedTool" class="tool-workspace">
          <a-card :title="`${getSelectedToolName()} 分析`">
            <!-- 鱼骨图工具 -->
            <FishboneChart
              v-if="selectedTool === 'fishbone'"
              :data="toolsData.fishbone"
              :readonly="readonly"
              :event-id="eventId"
              :analysis-id="analysisId"
              @data-change="handleFishboneDataChange"
            />

            <!-- 5Why分析工具 -->
            <FiveWhyAnalysis
              v-else-if="selectedTool === '5why'"
              :data="toolsData.fiveWhy"
              :readonly="readonly"
              :event-id="eventId"
              :analysis-id="analysisId"
              @data-change="handleFiveWhyDataChange"
            />

            <!-- PDCA分析工具 - 使用增强版组件 -->
            <EnhancedPDCAAnalysis
              v-else-if="selectedTool === 'pdca'"
              :data="toolsData.pdca"
              :event-info="{ id: eventId }"
              :readonly="readonly"
              @data-change="handlePdcaDataChange"
              @save="handlePDCASave"
              @export="handlePDCAExport"
              @export-format="handlePDCAMultiFormatExport"
            />

            <!-- FMEA分析工具 -->
            <FMEAAnalysis
              v-else-if="selectedTool === 'fmea'"
              :data="toolsData.fmea"
              :readonly="readonly"
              @data-change="handleFmeaDataChange"
            />

            <!-- 默认占位符 -->
            <div v-else class="tool-placeholder">
              <a-result
                status="info"
                title="分析工具工作区"
                :sub-title="`正在使用 ${getSelectedToolName()} 进行分析`"
              >
                <template #extra>
                  <a-button type="primary" @click="handleStartAnalysis">开始分析</a-button>
                </template>
              </a-result>
            </div>
          </a-card>
        </div>
      </div>
    </div>

    <!-- 分析结论标签页 -->
    <div v-show="activeTab === 'conclusion'" class="tab-content">
      <a-form layout="vertical" :model="analysisData.conclusion">
        <a-form-item label="分析结论">
          <a-textarea 
            v-model:value="analysisData.conclusion.summary"
            :rows="4"
            placeholder="请输入分析结论..."
            :disabled="readonly"
          />
        </a-form-item>
        <a-form-item label="改进建议">
          <a-textarea 
            v-model:value="analysisData.conclusion.recommendations"
            :rows="4"
            placeholder="请输入改进建议..."
            :disabled="readonly"
          />
        </a-form-item>
        <a-form-item label="预期效果">
          <a-textarea 
            v-model:value="analysisData.conclusion.expectedOutcome"
            :rows="3"
            placeholder="请描述预期改进效果..."
            :disabled="readonly"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  SaveOutlined,
  CheckOutlined,
  PlusOutlined,
  DeleteOutlined,
  BarChartOutlined,
  BulbOutlined,
  NodeIndexOutlined,
  BranchesOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import { ensureDayjsObject, createDatePickerProps } from '@/utils/dateUtils'

// 导入API方法
import { savePDCAData, getPDCAData, exportPDCAAnalysis } from '@/api/evt/analyze'

// 导入Excel导出工具
import { jsonToSheetXlsx, aoaToSheetXlsx } from '@/components/Excel'

// 导入多格式导出工具
import { MultiFormatExporter, ExportFormat, type ExportData } from '@/utils/export/multiFormatExporter'

// 导入分析工具组件
import FishboneChart from './FishboneChart.vue'
import FiveWhyAnalysis from './FiveWhyAnalysis.vue'
import EnhancedPDCAAnalysis from './EnhancedPDCAAnalysis.vue'
import FMEAAnalysis from './FMEAAnalysis.vue'
import CauseMeasureRelation from './CauseMeasureRelation.vue'
import EventInfoDisplay from './EventInfoDisplay.vue'

// 定义Props
interface Props {
  loading?: boolean
  readonly?: boolean
  initialData?: any
  eventId?: string
  analysisId?: string
  eventInfo?: any
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  readonly: false,
  initialData: () => ({})
})

// 定义Emits
const emit = defineEmits<{
  save: [data: any]
  submit: [data: any]
  dataChange: [data: any]
}>()

// 响应式数据
const activeTab = ref('eventInfo')
const saving = ref(false)
const submitting = ref(false)
const selectedTool = ref('')

// 工作区标签页配置
const workspaceTabs = [
  { key: 'eventInfo', tab: '事件信息' },
  { key: 'meeting', tab: '参会信息' },
  { key: 'causes', tab: '原因分析' },
  { key: 'measures', tab: '对策措施' },
  { key: 'relations', tab: '原因对策关联' },
  { key: 'tools', tab: '分析工具' },
  { key: 'conclusion', tab: '分析结论' }
]

// 分析工具配置
const analysisTools = [
  { key: 'pdca', name: 'PDCA分析', description: '计划-执行-检查-行动', icon: BarChartOutlined },
  { key: '5why', name: '5Why分析', description: '五个为什么分析法', icon: BulbOutlined },
  { key: 'fmea', name: 'FMEA分析', description: '失效模式与影响分析', icon: NodeIndexOutlined },
  { key: 'fishbone', name: '鱼骨图分析', description: '因果关系分析', icon: BranchesOutlined }
]



// 分析数据
const analysisData = reactive({
  meetingInfo: {
    meetingTime: null,
    location: '',
    moderator: '',
    participants: ''
  },
  causes: [],
  measures: [],
  relations: [], // 原因对策关联关系
  conclusion: {
    summary: '',
    recommendations: '',
    expectedOutcome: ''
  }
})

// 分析工具数据
const toolsData = reactive({
  fishbone: {
    problem: '',
    mainBones: [],
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0'
    }
  },
  fiveWhy: {
    problem: '',
    whyLevels: [],
    conclusion: '',
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0'
    }
  },
  pdca: {
    cycleName: '',
    plan: [],
    do: [],
    check: [],
    act: [],
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0'
    }
  },
  fmea: {
    processName: '',
    riskItems: [],
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0'
    }
  }
})

// 计算属性
const canSubmit = computed(() => {
  return analysisData.causes.length > 0 && 
         analysisData.measures.length > 0 && 
         analysisData.conclusion.summary
})

const getSelectedToolName = () => {
  const tool = analysisTools.find(t => t.key === selectedTool.value)
  return tool ? tool.name : ''
}

// 事件处理
const handleTabChange = (key: string) => {
  activeTab.value = key
}

const handleSave = async () => {
  saving.value = true
  try {
    emit('save', analysisData)
    message.success('保存成功')
  } catch (error) {
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleSubmit = async () => {
  submitting.value = true
  try {
    emit('submit', analysisData)
    message.success('提交成功')
  } catch (error) {
    message.error('提交失败')
  } finally {
    submitting.value = false
  }
}

const handleAddCause = () => {
  analysisData.causes.push({
    id: `cause_${Date.now()}`,
    type: 'direct',
    description: ''
  })
}

const handleRemoveCause = (index: number) => {
  analysisData.causes.splice(index, 1)
}

const handleAddMeasure = () => {
  analysisData.measures.push({
    id: `measure_${Date.now()}`,
    description: '',
    priority: 'medium',
    responsible: '',
    deadline: null // 保持为null，让用户选择时会自动转换为dayjs对象
  })
}

const handleRemoveMeasure = (index: number) => {
  analysisData.measures.splice(index, 1)
}

// 监听日期字段变化，确保格式正确
watch(() => analysisData.meetingInfo.meetingTime, (newValue) => {
  if (newValue && !dayjs.isDayjs(newValue)) {
    analysisData.meetingInfo.meetingTime = ensureDayjsObject(newValue)
  }
})

// 监听措施的deadline字段变化
watch(() => analysisData.measures, (newMeasures) => {
  newMeasures.forEach((measure, index) => {
    if (measure.deadline && !dayjs.isDayjs(measure.deadline)) {
      measure.deadline = ensureDayjsObject(measure.deadline)
    }
  })
}, { deep: true })

// 处理原因对策关联变化
const handleRelationsChange = (relations: any[]) => {
  analysisData.relations = relations
  emit('dataChange', { analysisData, toolsData })
}

const handleToolSelect = (tool: any) => {
  selectedTool.value = tool.key
  message.info(`已选择 ${tool.name}`)
}

const handleStartAnalysis = () => {
  message.info('开始分析功能开发中...')
}

// 分析工具数据变化处理
const handleFishboneDataChange = (data: any) => {
  Object.assign(toolsData.fishbone, data)

  // 从鱼骨图数据中提取原因
  if (data.mainBones && data.mainBones.length > 0) {
    syncCausesFromFishbone(data.mainBones)
  }

  emit('dataChange', { analysisData, toolsData })
}

const handleFiveWhyDataChange = (data: any) => {
  Object.assign(toolsData.fiveWhy, data)

  // 从5Why分析中提取原因
  if (data.whyLevels && data.whyLevels.length > 0) {
    syncCausesFromFiveWhy(data.whyLevels)
  }

  emit('dataChange', { analysisData, toolsData })
}

const handlePdcaDataChange = (data: any) => {
  Object.assign(toolsData.pdca, data)
  emit('dataChange', { analysisData, toolsData })
}

// PDCA保存方法
const handlePDCASave = async (data: any) => {
  try {
    console.log('保存PDCA数据:', data)

    // 构建保存请求数据
    const saveRequest = {
      eventId: props.eventId,
      analysisId: props.analysisId,
      toolType: 'pdca',
      toolName: 'PDCA循环分析',
      toolData: data,
      autoSave: true,
      mainProblem: data.title || '',
      conclusion: data.conclusion || ''
    }

    // 使用项目统一的HTTP客户端调用后端API保存数据
    const result = await savePDCAData(saveRequest)

    if (result) {
      message.success('PDCA分析已保存')
      console.log('PDCA保存成功，ID:', result)
      return result // 返回保存结果
    } else {
      throw new Error('保存失败')
    }
  } catch (error) {
    message.error('保存失败，请重试')
    console.error('PDCA保存失败:', error)
    throw error // 重新抛出错误，让调用方处理
  }
}

// 创建Excel格式的PDCA数据
const createExcelData = (data: any) => {
  const excelData = []

  // 添加报告头部信息
  excelData.push(['PDCA分析报告'])
  excelData.push(['事件编号', props.eventId || ''])
  excelData.push(['分析编号', props.analysisId || ''])
  excelData.push(['导出时间', new Date().toLocaleString('zh-CN')])
  excelData.push(['完成进度', `${data.completionPercentage || 0}%`])
  excelData.push([]) // 空行

  // 添加PDCA各阶段数据
  if (data.phases && Array.isArray(data.phases)) {
    data.phases.forEach((phase: any) => {
      if (['plan', 'do', 'check', 'act'].includes(phase.phase)) {
        excelData.push([`${phase.title || phase.phase}阶段`])
        excelData.push(['状态', phase.status || '未开始'])
        excelData.push(['负责人', phase.responsible || ''])
        excelData.push(['截止日期', phase.deadline ? new Date(phase.deadline).toLocaleDateString('zh-CN') : ''])
        excelData.push(['完成进度', `${phase.progress || 0}%`])
        excelData.push(['已完成项目', `${phase.completedItems || 0}/${phase.totalItems || 0}`])

        // 添加具体内容
        if (phase.content && phase.content.items && Array.isArray(phase.content.items)) {
          excelData.push(['具体内容'])
          phase.content.items.forEach((item: any, index: number) => {
            excelData.push([`${index + 1}. ${item.title || item.content || ''}`, item.completed ? '已完成' : '未完成'])
          })
        }

        excelData.push([]) // 空行分隔
      }
    })
  }

  return excelData
}

// PDCA导出方法 - 支持Excel和JSON格式
const handlePDCAExport = async (data: any) => {
  try {
    console.log('🔄 开始导出PDCA数据:', data)

    // 首先保存数据，确保最新数据已保存
    console.log('📝 保存最新数据...')
    const saveResult = await handlePDCASave(data)
    console.log('✅ 数据保存完成:', saveResult)

    // 获取PDCA数据ID（从最近保存的数据中获取）
    console.log('🔍 获取PDCA数据ID...')
    const pdcaResult = await getPDCAData({
      eventId: props.eventId,
      analysisId: props.analysisId || ''
    })
    console.log('📦 获取到PDCA数据:', pdcaResult)

    // 准备导出数据
    let exportData = data
    let dataSource = '当前编辑数据'

    if (pdcaResult && pdcaResult.id) {
      try {
        // 尝试从API获取完整数据
        const apiResult = await exportPDCAAnalysis(pdcaResult.id)
        if (apiResult) {
          exportData = apiResult
          dataSource = 'API数据'
          console.log('✅ 使用API数据导出')
        }
      } catch (apiError) {
        console.warn('⚠️ API导出失败，使用本地数据:', apiError)
      }
    }

    // 生成文件名
    const timestamp = new Date().toISOString().split('T')[0]
    const eventType = '患者跌倒' // 可以从事件信息中获取

    // 导出Excel格式（推荐格式）
    try {
      const excelData = createExcelData(exportData)
      const filename = `PDCA分析报告_${eventType}_${props.eventId}_${timestamp}_V1.0.xlsx`

      // 创建自定义Excel导出，使用短工作表名称
      const { utils, writeFile } = await import('xlsx')
      const worksheet = utils.aoa_to_sheet(excelData)

      // 使用短工作表名称（不超过31字符）
      const shortSheetName = 'PDCA分析报告'
      const workbook = {
        SheetNames: [shortSheetName],
        Sheets: {
          [shortSheetName]: worksheet,
        },
      }

      writeFile(workbook, filename, { bookType: 'xlsx' })

      message.success(`PDCA分析报告已导出为Excel格式 (${dataSource})`)
      console.log('✅ Excel文件导出完成')
    } catch (excelError) {
      console.error('❌ Excel导出失败，降级为JSON导出:', excelError)

      // 如果Excel导出失败，降级为JSON导出
      const jsonExportData = {
        title: '事件PDCA分析报告',
        eventId: props.eventId,
        analysisId: props.analysisId,
        exportTime: new Date().toISOString(),
        pdcaData: exportData,
        dataSource: dataSource,
        note: dataSource === 'API数据' ? '完整的PDCA分析报告' : '基于当前编辑数据生成的报告'
      }

      const blob = new Blob([JSON.stringify(jsonExportData, null, 2)], {
        type: 'application/json;charset=utf-8'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `PDCA分析报告_${eventType}_${props.eventId}_${timestamp}_V1.0.json`
      a.style.display = 'none'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      message.warning(`Excel导出失败，已导出JSON格式 (${dataSource})`)
      console.log('✅ JSON文件导出完成')
    }
  } catch (error) {
    console.error('❌ PDCA导出完全失败:', error)
    message.error(`导出失败：${error.message || '未知错误'}`)
  }
}

// 多格式PDCA导出方法 - 支持Excel、Word、PDF格式
const handlePDCAMultiFormatExport = async (data: any, format: ExportFormat = ExportFormat.EXCEL) => {
  try {
    console.log(`🔄 开始导出PDCA数据为${format}格式:`, data)

    // 首先保存数据，确保最新数据已保存
    console.log('📝 保存最新数据...')
    const saveResult = await handlePDCASave(data)
    console.log('✅ 数据保存完成:', saveResult)

    // 获取PDCA数据ID（从最近保存的数据中获取）
    console.log('🔍 获取PDCA数据ID...')
    const pdcaResult = await getPDCAData({
      eventId: props.eventId,
      analysisId: props.analysisId || ''
    })
    console.log('📦 获取到PDCA数据:', pdcaResult)

    // 准备导出数据
    let exportData = data
    let dataSource = '当前编辑数据'

    if (pdcaResult && pdcaResult.id) {
      try {
        // 尝试从API获取完整数据
        const apiResult = await exportPDCAAnalysis(pdcaResult.id)
        if (apiResult) {
          exportData = apiResult
          dataSource = 'API数据'
          console.log('✅ 使用API数据导出')
        }
      } catch (apiError) {
        console.warn('⚠️ API导出失败，使用本地数据:', apiError)
      }
    }

    // 转换数据格式为标准导出格式
    const standardExportData: ExportData = {
      title: exportData.title || '事件PDCA分析报告',
      eventId: props.eventId,
      analysisId: props.analysisId,
      exportTime: new Date().toISOString(),
      completionPercentage: exportData.completionPercentage || 0,
      phases: exportData.phases || [],
      metadata: {
        version: '1.0.0',
        exportedBy: '系统用户',
        analysisType: 'PDCA循环分析',
        eventId: props.eventId || '',
        analysisId: props.analysisId || ''
      }
    }

    // 获取事件类型
    const eventType = '患者跌倒' // 可以从事件信息中获取

    // 使用多格式导出器
    await MultiFormatExporter.export(standardExportData, format, eventType)

    console.log(`✅ ${format}格式文件导出完成 (${dataSource})`)
  } catch (error) {
    console.error(`❌ ${format}格式导出失败:`, error)
    message.error(`${format}格式导出失败：${error.message || '未知错误'}`)
  }
}

const handleFmeaDataChange = (data: any) => {
  Object.assign(toolsData.fmea, data)
  emit('dataChange', { analysisData, toolsData })
}

// 数据同步方法
const syncCausesFromFishbone = (mainBones: any[]) => {
  const newCauses: any[] = []

  mainBones.forEach(bone => {
    if (bone.causes && bone.causes.length > 0) {
      bone.causes.forEach((cause: any) => {
        // 检查是否已存在相同的原因
        const existingCause = analysisData.causes.find(c =>
          c.causeDescription === cause.text || c.description === cause.text
        )

        if (!existingCause && cause.text && cause.text.trim()) {
          newCauses.push({
            id: `cause_fishbone_${Date.now()}_${Math.random()}`,
            causeType: mapBoneCategoryToCauseType(bone.category),
            causeDescription: cause.text,
            level: 2, // 鱼骨图的原因默认为直接原因
            probability: 70, // 默认可能性
            impact: 3, // 默认影响程度
            evidence: '来源：鱼骨图分析',
            source: 'fishbone'
          })
        }
      })
    }
  })

  if (newCauses.length > 0) {
    analysisData.causes.push(...newCauses)
    message.success(`从鱼骨图同步了${newCauses.length}个原因`)
  }
}

const syncCausesFromFiveWhy = (whyLevels: any[]) => {
  const newCauses: any[] = []

  whyLevels.forEach((level, index) => {
    if (level.question && level.question.trim()) {
      // 检查是否已存在相同的原因
      const existingCause = analysisData.causes.find(c =>
        c.causeDescription === level.question || c.description === level.question
      )

      if (!existingCause) {
        newCauses.push({
          id: `cause_fivewhy_${Date.now()}_${Math.random()}`,
          causeType: '管理因素', // 5Why分析通常涉及管理因素
          causeDescription: level.question,
          level: Math.min(index + 1, 4), // 根据层级确定原因层级
          probability: Math.max(90 - index * 15, 30), // 越深层的原因可能性越低
          impact: Math.min(index + 2, 5), // 越深层的原因影响越大
          evidence: '来源：5Why分析',
          source: 'fiveWhy'
        })
      }
    }
  })

  if (newCauses.length > 0) {
    analysisData.causes.push(...newCauses)
    message.success(`从5Why分析同步了${newCauses.length}个原因`)
  }
}

// 映射鱼骨图分类到原因类型
const mapBoneCategoryToCauseType = (category: string): string => {
  const mapping: Record<string, string> = {
    'people': '人员因素',
    'machine': '设备因素',
    'material': '物料因素',
    'method': '方法因素',
    'environment': '环境因素',
    'measurement': '测量因素',
    'management': '管理因素'
  }
  return mapping[category] || '其他因素'
}

// 监听分析数据变化
watch(analysisData, () => {
  emit('dataChange', { analysisData, toolsData })
}, { deep: true })

// 初始化数据
if (props.initialData) {
  if (props.initialData.analysisData) {
    Object.assign(analysisData, props.initialData.analysisData)
  }
  if (props.initialData.toolsData) {
    Object.assign(toolsData, props.initialData.toolsData)
  }
}
</script>

<style scoped lang="less">
.analysis-workspace {
  .tab-content {
    padding: 8px 0; // 减少垂直padding从16px到8px
    min-height: 400px;
    max-height: calc(100vh - 280px); // 增加可用高度，从300px减少到280px
    overflow: auto; // 修复：允许滚动，解决PDCA分析工具页面无法滚动的问题

    // 为分析工具标签页特殊优化
    &:has(.tools-section) {
      padding: 4px 0; // 分析工具页面进一步减少padding
    }

    // 为事件信息标签页特殊处理
    &:has(.event-info-display) {
      padding: 0;
      height: calc(100vh - 300px);
      overflow: hidden; // 事件信息页面保持原有的overflow设置
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      color: #333;
    }
  }

  .causes-list, .measures-list {
    .cause-item, .measure-item {
      margin-bottom: 12px;
    }
  }

  .tools-section {
    // 紧凑的工具选择器布局
    .tools-selector {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .tool-card-compact {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fff;
      min-width: 160px;
      flex: 1;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
      }

      &.selected {
        border-color: #1890ff;
        background-color: #f6ffed;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
      }

      .tool-icon-compact {
        font-size: 18px;
        color: #1890ff;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .tool-info-compact {
        flex: 1;
        text-align: left;

        .tool-name-compact {
          font-weight: 500;
          font-size: 13px;
          line-height: 1.2;
          margin-bottom: 2px;
          color: #333;
        }

        .tool-desc-compact {
          font-size: 11px;
          color: #666;
          line-height: 1.2;
        }
      }
    }

    .tool-workspace {
      margin-top: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .analysis-workspace {
    .tools-section {
      .tools-selector {
        flex-direction: column;
        gap: 6px;
      }

      .tool-card-compact {
        min-width: auto;
        flex: none;
      }
    }
  }
}

@media (max-width: 480px) {
  .analysis-workspace {
    .tools-section {
      .tool-card-compact {
        .tool-info-compact {
          .tool-name-compact {
            font-size: 12px;
          }

          .tool-desc-compact {
            font-size: 10px;
          }
        }
      }
    }
  }
}
</style>
