package com.elkj.nms.module.evt.controller.admin.analyze.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * PDCA分析数据 VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
public class PDCAAnalysisVO {

    @Schema(description = "保存PDCA分析数据 Request VO")
    @Data
    public static class SaveReqVO {
        
        @Schema(description = "事件ID", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "事件ID不能为空")
        private String eventId;
        
        @Schema(description = "分析ID")
        private String analysisId;
        
        @Schema(description = "工具类型", example = "pdca")
        private String toolType = "pdca";
        
        @Schema(description = "工具名称", example = "PDCA循环分析")
        private String toolName = "PDCA循环分析";
        
        @Schema(description = "PDCA数据", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "PDCA数据不能为空")
        private Map<String, Object> toolData;
        
        @Schema(description = "是否自动保存", example = "true")
        private Boolean autoSave = false;
        
        @Schema(description = "主要问题描述")
        private String mainProblem;
        
        @Schema(description = "分析结论")
        private String conclusion;
    }

    @Schema(description = "更新PDCA分析数据 Request VO")
    @Data
    public static class UpdateReqVO {
        
        @Schema(description = "PDCA数据", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "PDCA数据不能为空")
        private Map<String, Object> toolData;
        
        @Schema(description = "主要问题描述")
        private String mainProblem;
        
        @Schema(description = "分析结论")
        private String conclusion;
        
        @Schema(description = "状态", example = "draft")
        private String status;
    }

    @Schema(description = "PDCA分析数据 Response VO")
    @Data
    public static class RespVO {
        
        @Schema(description = "PDCA数据ID")
        private String id;
        
        @Schema(description = "事件ID")
        private String eventId;
        
        @Schema(description = "分析ID")
        private String analysisId;
        
        @Schema(description = "工具类型")
        private String toolType;
        
        @Schema(description = "工具名称")
        private String toolName;
        
        @Schema(description = "PDCA数据")
        private Map<String, Object> toolData;
        
        @Schema(description = "主要问题描述")
        private String mainProblem;
        
        @Schema(description = "分析结论")
        private String conclusion;
        
        @Schema(description = "状态")
        private String status;
        
        @Schema(description = "版本号")
        private Integer version;
        
        @Schema(description = "创建人ID")
        private String creatorUserId;
        
        @Schema(description = "创建人姓名")
        private String creatorUserName;
        
        @Schema(description = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;
        
        @Schema(description = "更新时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updateTime;
    }

    @Schema(description = "PDCA分析列表 Response VO")
    @Data
    public static class ListRespVO {
        
        @Schema(description = "PDCA数据ID")
        private String id;
        
        @Schema(description = "事件ID")
        private String eventId;
        
        @Schema(description = "事件名称")
        private String eventName;
        
        @Schema(description = "事件类型")
        private String eventType;
        
        @Schema(description = "工具名称")
        private String toolName;
        
        @Schema(description = "主要问题描述")
        private String mainProblem;
        
        @Schema(description = "状态")
        private String status;
        
        @Schema(description = "完成进度")
        private Integer progress;
        
        @Schema(description = "创建人姓名")
        private String creatorUserName;
        
        @Schema(description = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;
        
        @Schema(description = "更新时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updateTime;
    }

    @Schema(description = "PDCA阶段数据 VO")
    @Data
    public static class PhaseDataVO {
        
        @Schema(description = "阶段状态")
        private String status;
        
        @Schema(description = "负责人")
        private String responsible;
        
        @Schema(description = "截止时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private String deadline;
        
        @Schema(description = "阶段内容")
        private Map<String, Object> content;
    }

    @Schema(description = "PDCA完整数据结构 VO")
    @Data
    public static class PDCADataVO {
        
        @Schema(description = "PDCA标题")
        private String title;
        
        @Schema(description = "计划阶段")
        private PhaseDataVO plan;
        
        @Schema(description = "执行阶段")
        private PhaseDataVO doPhase;
        
        @Schema(description = "检查阶段")
        private PhaseDataVO check;
        
        @Schema(description = "行动阶段")
        private PhaseDataVO act;
        
        @Schema(description = "整体进度")
        private Integer overallProgress;
        
        @Schema(description = "选择的模板")
        private String selectedTemplate;
    }

    @Schema(description = "医疗模板 VO")
    @Data
    public static class TemplateVO {
        
        @Schema(description = "模板ID")
        private String id;
        
        @Schema(description = "事件类型")
        private String eventType;
        
        @Schema(description = "模板名称")
        private String name;
        
        @Schema(description = "模板描述")
        private String description;
        
        @Schema(description = "模板数据")
        private Map<String, Object> template;
        
        @Schema(description = "适用场景")
        private String applicableScenarios;
        
        @Schema(description = "使用次数")
        private Integer usageCount;
        
        @Schema(description = "评分")
        private Double rating;
    }

    @Schema(description = "智能建议 VO")
    @Data
    public static class SmartSuggestionVO {
        
        @Schema(description = "建议内容")
        private String content;
        
        @Schema(description = "建议类型")
        private String type;
        
        @Schema(description = "相关度评分")
        private Double relevanceScore;
        
        @Schema(description = "来源")
        private String source;
        
        @Schema(description = "适用阶段")
        private String phase;
    }

    @Schema(description = "导出数据 VO")
    @Data
    public static class ExportVO {
        
        @Schema(description = "报告标题")
        private String title;
        
        @Schema(description = "事件信息")
        private Map<String, Object> eventInfo;
        
        @Schema(description = "PDCA分析数据")
        private PDCADataVO analysisData;
        
        @Schema(description = "完成百分比")
        private Integer completionPercentage;
        
        @Schema(description = "导出时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime exportTime;
        
        @Schema(description = "导出人")
        private String exportUser;
        
        @Schema(description = "使用的模板")
        private String template;
        
        @Schema(description = "分析总结")
        private String summary;
    }
}
