package com.elkj.nms.module.evt.service.analyze;

import com.elkj.nms.framework.common.pojo.PageResult;
import com.elkj.nms.module.evt.controller.admin.analyze.vo.PDCAAnalysisVO;

import java.util.List;
import java.util.Map;

/**
 * PDCA分析管理 Service 接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
public interface PDCAAnalysisService {

    /**
     * 保存PDCA分析数据
     *
     * @param request 保存请求
     * @return PDCA数据ID
     */
    String savePDCAAnalysis(PDCAAnalysisVO.SaveReqVO request);

    /**
     * 获取事件的PDCA分析数据
     *
     * @param eventId    事件ID
     * @param analysisId 分析ID（可选）
     * @return PDCA分析数据
     */
    PDCAAnalysisVO.RespVO getPDCAAnalysis(String eventId, String analysisId);

    /**
     * 更新PDCA分析数据
     *
     * @param id      PDCA数据ID
     * @param request 更新请求
     */
    void updatePDCAAnalysis(String id, PDCAAnalysisVO.UpdateReqVO request);

    /**
     * 删除PDCA分析数据
     *
     * @param id PDCA数据ID
     */
    void deletePDCAAnalysis(String id);

    /**
     * 完成PDCA分析
     *
     * @param id PDCA数据ID
     */
    void completePDCAAnalysis(String id);

    /**
     * 导出PDCA分析报告
     *
     * @param id PDCA数据ID
     * @return 导出数据
     */
    Map<String, Object> exportPDCAAnalysis(String id);

    /**
     * 获取所有医疗事件模板
     *
     * @return 模板列表
     */
    List<Map<String, Object>> getAllTemplates();

    /**
     * 获取特定类型的模板
     *
     * @param eventType 事件类型
     * @return 模板数据
     */
    Map<String, Object> getTemplateByEventType(String eventType);

    /**
     * 获取智能建议
     *
     * @param eventType 事件类型
     * @param phase     PDCA阶段
     * @return 智能建议列表
     */
    List<String> getSmartSuggestions(String eventType, String phase);

    /**
     * 分页查询PDCA分析数据
     *
     * @param pageNum   页码
     * @param pageSize  页大小
     * @param eventType 事件类型（可选）
     * @param status    状态（可选）
     * @return 分页结果
     */
    PageResult<PDCAAnalysisVO.ListRespVO> getPDCAAnalysisList(Integer pageNum, Integer pageSize, 
                                                              String eventType, String status);

    /**
     * 根据事件信息推荐模板
     *
     * @param eventId   事件ID
     * @param eventType 事件类型
     * @param eventName 事件名称
     * @return 推荐的模板
     */
    Map<String, Object> recommendTemplate(String eventId, String eventType, String eventName);

    /**
     * 获取PDCA分析统计信息
     *
     * @param eventType 事件类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate   结束日期（可选）
     * @return 统计信息
     */
    Map<String, Object> getPDCAStatistics(String eventType, String startDate, String endDate);

    /**
     * 批量导入PDCA模板
     *
     * @param templates 模板数据列表
     * @return 导入结果
     */
    Map<String, Object> batchImportTemplates(List<Map<String, Object>> templates);

    /**
     * 获取PDCA分析历史版本
     *
     * @param eventId 事件ID
     * @return 历史版本列表
     */
    List<PDCAAnalysisVO.RespVO> getPDCAAnalysisHistory(String eventId);

    /**
     * 复制PDCA分析数据
     *
     * @param sourceId  源PDCA数据ID
     * @param targetEventId 目标事件ID
     * @return 新的PDCA数据ID
     */
    String copyPDCAAnalysis(String sourceId, String targetEventId);

    /**
     * 验证PDCA数据完整性
     *
     * @param pdcaData PDCA数据
     * @return 验证结果
     */
    Map<String, Object> validatePDCAData(Map<String, Object> pdcaData);

    /**
     * 获取PDCA阶段完成情况
     *
     * @param id PDCA数据ID
     * @return 阶段完成情况
     */
    Map<String, Object> getPDCAPhaseCompletion(String id);

    /**
     * 更新PDCA阶段状态
     *
     * @param id    PDCA数据ID
     * @param phase 阶段名称
     * @param status 新状态
     */
    void updatePDCAPhaseStatus(String id, String phase, String status);

    /**
     * 获取PDCA分析效果评估
     *
     * @param id PDCA数据ID
     * @return 效果评估数据
     */
    Map<String, Object> getPDCAEffectivenessEvaluation(String id);

    /**
     * 保存PDCA分析效果评估
     *
     * @param id             PDCA数据ID
     * @param evaluationData 评估数据
     */
    void savePDCAEffectivenessEvaluation(String id, Map<String, Object> evaluationData);
}
