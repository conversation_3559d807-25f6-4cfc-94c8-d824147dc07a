<!--
  增强版PDCA分析工具组件
  
  优化特性：
  1. 简化操作流程 - 步骤导航 + 智能引导
  2. 紧凑布局设计 - 左右分栏，减少滚动
  3. 智能化功能 - 医疗模板 + 自动建议
  4. 用户体验优化 - 实时保存 + 进度反馈
  
  <AUTHOR>
  @version 3.0.0
  @since 2025-08-02
-->

<template>
  <div class="enhanced-pdca-analysis">
    <!-- 顶部工具栏 -->
    <div class="analysis-header">
      <div class="header-left">
        <a-input
          v-model:value="localData.title"
          placeholder="请输入PDCA分析主题"
          style="width: 400px"
          @change="handleDataChange"
          :disabled="readonly"
        >
          <template #prefix>
            <FileTextOutlined />
          </template>
        </a-input>
        
        <!-- 模板选择器 -->
        <a-select
          v-model:value="selectedTemplate"
          placeholder="选择医疗事件模板"
          style="width: 200px"
          @change="handleTemplateChange"
          :disabled="readonly"
        >
          <a-select-option value="">自定义分析</a-select-option>
          <a-select-option value="fall">跌倒事件</a-select-option>
          <a-select-option value="medication">用药错误</a-select-option>
          <a-select-option value="infection">感染事件</a-select-option>
          <a-select-option value="surgery">手术事件</a-select-option>
          <a-select-option value="communication">沟通问题</a-select-option>
        </a-select>
      </div>

      <div class="header-right">
        <!-- 进度指示 -->
        <div class="progress-indicator">
          <a-progress
            :percent="completionPercentage"
            size="small"
            :show-info="false"
            :stroke-color="getProgressColor()"
          />
          <span class="progress-text">{{ completionPercentage }}% 完成</span>
        </div>

        <!-- 操作按钮 -->
        <a-space>
          <a-button size="small" @click="showHelp" :icon="h(QuestionCircleOutlined)">
            帮助
          </a-button>
          <a-button size="small" @click="handleReset" :disabled="readonly" :loading="isResetting">
            <template #icon><ReloadOutlined /></template>
            重置
          </a-button>
          <a-dropdown :trigger="['click']">
            <a-button size="small" :loading="isExporting">
              <template #icon><ExportOutlined /></template>
              导出
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleExportMenuClick">
                <a-menu-item key="excel">
                  <template #icon><FileExcelOutlined /></template>
                  Excel格式 (.xlsx)
                </a-menu-item>
                <a-menu-item key="word">
                  <template #icon><FileWordOutlined /></template>
                  Word格式 (.docx)
                </a-menu-item>
                <a-menu-item key="pdf">
                  <template #icon><FilePdfOutlined /></template>
                  PDF格式 (.pdf)
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </div>

    <!-- 步骤导航 -->
    <div class="steps-navigation">
      <a-steps :current="currentStepIndex" size="small" @change="handleStepChange">
        <a-step 
          v-for="(step, index) in steps" 
          :key="step.key"
          :title="step.title"
          :description="step.description"
          :status="getStepStatus(index)"
        >
          <template #icon>
            <span class="step-icon">{{ step.icon }}</span>
          </template>
        </a-step>
      </a-steps>
    </div>

    <!-- 主工作区 -->
    <div class="main-workspace">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <!-- 紧凑循环图 -->
        <div class="compact-cycle">
          <div class="cycle-container">
            <div class="cycle-center">
              <div class="center-title">PDCA</div>
              <div class="center-subtitle">持续改进</div>
            </div>
            
            <div 
              v-for="(step, index) in steps" 
              :key="step.key"
              class="cycle-step"
              :class="{ 
                active: currentStep === step.key,
                completed: isStepCompleted(step.key)
              }"
              :style="getStepPosition(index)"
              @click="switchToStep(step.key)"
            >
              <div class="step-content">
                <div class="step-icon">{{ step.icon }}</div>
                <div class="step-label">{{ step.shortTitle }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 当前阶段信息 -->
        <div class="phase-info-card">
          <a-card size="small" :title="currentPhaseInfo.title">
            <div class="phase-description">
              {{ currentPhaseInfo.description }}
            </div>
            
            <!-- 智能提示 -->
            <div v-if="smartSuggestions.length > 0" class="smart-suggestions">
              <h5>💡 智能建议</h5>
              <ul>
                <li v-for="suggestion in smartSuggestions" :key="suggestion">
                  {{ suggestion }}
                </li>
              </ul>
            </div>

            <!-- 完成度检查 -->
            <div class="completion-check">
              <a-progress 
                :percent="getPhaseCompletion(currentStep)" 
                size="small"
                :status="getPhaseCompletion(currentStep) === 100 ? 'success' : 'active'"
              />
              <div class="check-items">
                <div 
                  v-for="item in currentPhaseInfo.checkItems" 
                  :key="item"
                  class="check-item"
                  :class="{ completed: isCheckItemCompleted(item) }"
                >
                  <CheckCircleOutlined v-if="isCheckItemCompleted(item)" />
                  <span>{{ item }}</span>
                </div>
              </div>
            </div>
          </a-card>
        </div>
      </div>

      <!-- 右侧表单区域 -->
      <div class="right-panel">
        <div class="smart-form-container">
          <a-card :title="`${currentPhaseInfo.title} - 详细内容`" size="small">
            <a-form layout="vertical" :model="localData[currentStep]">
              <!-- 动态表单字段 -->
              <div v-for="field in currentPhaseInfo.fields" :key="field.key">
                <a-form-item 
                  :label="field.label"
                  :required="field.required"
                  :help="field.help"
                >
                  <!-- 文本域输入 -->
                  <a-textarea
                    v-if="field.type === 'textarea'"
                    v-model:value="localData[currentStep][field.key]"
                    :placeholder="field.placeholder"
                    :rows="field.rows || 4"
                    :maxlength="field.maxlength || 500"
                    show-count
                    :readonly="readonly"
                    @change="handleFieldChange(field.key)"
                  />
                  
                  <!-- 选择器 -->
                  <a-select
                    v-else-if="field.type === 'select'"
                    v-model:value="localData[currentStep][field.key]"
                    :placeholder="field.placeholder"
                    :options="field.options"
                    :disabled="readonly"
                    @change="handleFieldChange(field.key)"
                  />
                  
                  <!-- 日期选择 -->
                  <a-date-picker
                    v-else-if="field.type === 'date'"
                    v-model:value="localData[currentStep][field.key]"
                    :placeholder="field.placeholder"
                    style="width: 100%"
                    :disabled="readonly"
                    @change="handleFieldChange(field.key)"
                  />
                  
                  <!-- 默认文本输入 -->
                  <a-input
                    v-else
                    v-model:value="localData[currentStep][field.key]"
                    :placeholder="field.placeholder"
                    :disabled="readonly"
                    @change="handleFieldChange(field.key)"
                  />

                  <!-- 智能建议按钮 -->
                  <div v-if="field.smartSuggestions && field.smartSuggestions.length > 0" class="field-suggestions">
                    <a-button 
                      size="small" 
                      type="link" 
                      @click="showFieldSuggestions(field)"
                    >
                      查看建议 ({{ field.smartSuggestions.length }})
                    </a-button>
                  </div>
                </a-form-item>
              </div>

              <!-- 阶段状态管理 -->
              <a-form-item label="阶段状态">
                <a-radio-group 
                  v-model:value="localData[currentStep].status" 
                  @change="handleStatusChange"
                  :disabled="readonly"
                >
                  <a-radio value="pending">待开始</a-radio>
                  <a-radio value="in_progress">进行中</a-radio>
                  <a-radio value="completed">已完成</a-radio>
                </a-radio-group>
              </a-form-item>

              <!-- 责任人和截止时间 -->
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="责任人">
                    <a-input
                      v-model:value="localData[currentStep].responsible"
                      placeholder="请输入责任人"
                      :disabled="readonly"
                      @change="handleDataChange"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="截止时间">
                    <a-date-picker
                      v-model:value="localData[currentStep].deadline"
                      placeholder="请选择截止时间"
                      style="width: 100%"
                      :disabled="readonly"
                      @change="handleDataChange"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-card>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <!-- 自动保存状态 -->
        <div class="auto-save-status" v-if="!readonly">
          <a-spin v-if="isSaving" size="small" />
          <CheckCircleOutlined v-else-if="lastSaved" style="color: #52c41a" />
          <span class="save-text">
            {{ isSaving ? '保存中...' : lastSaved ? `已保存 ${formatTime(lastSaved)}` : '未保存' }}
          </span>
        </div>
      </div>

      <div class="action-right">
        <a-space>
          <a-button 
            @click="handlePrevStep" 
            :disabled="currentStepIndex === 0 || readonly"
          >
            上一步
          </a-button>
          <a-button 
            type="primary" 
            @click="handleNextStep"
            :disabled="readonly"
          >
            {{ currentStepIndex === steps.length - 1 ? '完成分析' : '下一步' }}
          </a-button>
        </a-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, h, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  FileTextOutlined,
  ReloadOutlined,
  ExportOutlined,
  CheckCircleOutlined,
  QuestionCircleOutlined,
  DownOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FilePdfOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import { ensureDayjsObject } from '@/utils/dateUtils'
import {
  savePDCAData,
  getPDCAData,
  updatePDCAData,
  getPDCATemplateByEventType,
  getPDCASmartSuggestions
} from '@/api/evt/analyze'

// 导入多格式导出工具
import { MultiFormatExporter, ExportFormat, type ExportData } from '@/utils/export/multiFormatExporter'

// 接口定义
interface PhaseData {
  status: 'pending' | 'in_progress' | 'completed'
  responsible: string
  deadline: any
  [key: string]: any
}

interface PDCAData {
  title: string
  plan: PhaseData & {
    objectives: string
    methods: string
    timeline: string
  }
  do: PhaseData & {
    actions: string
    resources: string
    progress: string
  }
  check: PhaseData & {
    metrics: string
    results: string
    evaluation: string
  }
  act: PhaseData & {
    improvements: string
    standardization: string
    nextCycle: string
  }
}

interface Props {
  data?: PDCAData
  readonly?: boolean
  eventInfo?: any
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  data: () => ({
    title: '',
    plan: { status: 'pending', responsible: '', deadline: null, objectives: '', methods: '', timeline: '' },
    do: { status: 'pending', responsible: '', deadline: null, actions: '', resources: '', progress: '' },
    check: { status: 'pending', responsible: '', deadline: null, metrics: '', results: '', evaluation: '' },
    act: { status: 'pending', responsible: '', deadline: null, improvements: '', standardization: '', nextCycle: '' }
  })
})

const emit = defineEmits<{
  'data-change': [data: PDCAData]
  save: [data: PDCAData]
  export: [data: any]
  'export-format': [data: any, format: ExportFormat]
}>()

// 响应式数据
const localData = reactive<PDCAData>({ ...props.data })
const currentStep = ref<'plan' | 'do' | 'check' | 'act'>('plan')
const selectedTemplate = ref('')
const isSaving = ref(false)
const isResetting = ref(false)
const isExporting = ref(false)
const lastSaved = ref<Date | null>(null)

// 步骤定义
const steps = [
  { 
    key: 'plan', 
    title: 'Plan 计划', 
    shortTitle: 'Plan',
    description: '制定改进计划和目标',
    icon: '📋' 
  },
  { 
    key: 'do', 
    title: 'Do 执行', 
    shortTitle: 'Do',
    description: '实施改进措施',
    icon: '🚀' 
  },
  { 
    key: 'check', 
    title: 'Check 检查', 
    shortTitle: 'Check',
    description: '检查改进效果',
    icon: '🔍' 
  },
  { 
    key: 'act', 
    title: 'Act 行动', 
    shortTitle: 'Act',
    description: '标准化成功做法',
    icon: '⚡' 
  }
]

// 计算属性
const currentStepIndex = computed(() => {
  return steps.findIndex(step => step.key === currentStep.value)
})

const completionPercentage = computed(() => {
  const phases = ['plan', 'do', 'check', 'act']
  let completedCount = 0

  phases.forEach(phase => {
    const phaseData = localData[phase]
    if (phaseData.status === 'completed') {
      completedCount++
    } else if (phaseData.status === 'in_progress') {
      completedCount += 0.5
    }
  })

  return Math.round((completedCount / phases.length) * 100)
})

// 阶段信息映射
const phaseInfoMap = {
  plan: {
    title: 'Plan - 计划阶段',
    description: '分析问题，制定改进计划和目标',
    checkItems: ['明确改进目标', '制定实施方法', '确定时间计划'],
    fields: [
      {
        key: 'objectives',
        label: '改进目标',
        type: 'textarea',
        placeholder: '请描述具体的改进目标和期望达到的效果',
        required: true,
        rows: 3,
        maxlength: 300,
        help: '建议使用SMART原则制定目标（具体、可衡量、可达成、相关、有时限）',
        smartSuggestions: []
      },
      {
        key: 'methods',
        label: '实施方法',
        type: 'textarea',
        placeholder: '请描述具体的实施方法和步骤',
        required: true,
        rows: 3,
        maxlength: 300,
        smartSuggestions: []
      },
      {
        key: 'timeline',
        label: '时间计划',
        type: 'textarea',
        placeholder: '请制定详细的时间计划和里程碑',
        required: false,
        rows: 2,
        maxlength: 200,
        smartSuggestions: []
      }
    ]
  },
  do: {
    title: 'Do - 执行阶段',
    description: '按照计划执行改进措施',
    checkItems: ['执行改进行动', '配置必要资源', '记录执行过程'],
    fields: [
      {
        key: 'actions',
        label: '执行行动',
        type: 'textarea',
        placeholder: '请描述具体执行的改进行动',
        required: true,
        rows: 3,
        maxlength: 300,
        smartSuggestions: []
      },
      {
        key: 'resources',
        label: '资源配置',
        type: 'textarea',
        placeholder: '请描述所需的人力、物力、财力等资源',
        required: true,
        rows: 3,
        maxlength: 300,
        smartSuggestions: []
      },
      {
        key: 'progress',
        label: '执行进度',
        type: 'textarea',
        placeholder: '请记录执行过程中的进度和遇到的问题',
        required: false,
        rows: 2,
        maxlength: 200,
        smartSuggestions: []
      }
    ]
  },
  check: {
    title: 'Check - 检查阶段',
    description: '检查改进效果，评估是否达到预期目标',
    checkItems: ['设定评估指标', '收集结果数据', '分析改进效果'],
    fields: [
      {
        key: 'metrics',
        label: '评估指标',
        type: 'textarea',
        placeholder: '请设定具体的评估指标和测量方法',
        required: true,
        rows: 3,
        maxlength: 300,
        smartSuggestions: []
      },
      {
        key: 'results',
        label: '结果数据',
        type: 'textarea',
        placeholder: '请记录收集到的结果数据和观察',
        required: true,
        rows: 3,
        maxlength: 300,
        smartSuggestions: []
      },
      {
        key: 'evaluation',
        label: '效果评估',
        type: 'textarea',
        placeholder: '请分析改进效果，是否达到预期目标',
        required: false,
        rows: 2,
        maxlength: 200,
        smartSuggestions: []
      }
    ]
  },
  act: {
    title: 'Act - 行动阶段',
    description: '根据检查结果采取行动，标准化成功做法',
    checkItems: ['制定改进措施', '标准化流程', '准备下轮循环'],
    fields: [
      {
        key: 'improvements',
        label: '改进措施',
        type: 'textarea',
        placeholder: '请描述进一步的改进措施',
        required: true,
        rows: 3,
        maxlength: 300,
        smartSuggestions: []
      },
      {
        key: 'standardization',
        label: '标准化',
        type: 'textarea',
        placeholder: '请描述如何标准化成功做法',
        required: true,
        rows: 3,
        maxlength: 300,
        smartSuggestions: []
      },
      {
        key: 'nextCycle',
        label: '下轮循环',
        type: 'textarea',
        placeholder: '请规划下一轮PDCA循环的重点',
        required: false,
        rows: 2,
        maxlength: 200,
        smartSuggestions: []
      }
    ]
  }
}

const currentPhaseInfo = computed(() => phaseInfoMap[currentStep.value])

// 智能建议
const smartSuggestions = computed(() => {
  const suggestions = []
  const template = getTemplateByType(selectedTemplate.value)

  if (template && template.template[currentStep.value]) {
    const phaseTemplate = template.template[currentStep.value]
    if (phaseTemplate.smartSuggestions) {
      suggestions.push(...phaseTemplate.smartSuggestions)
    }
  }

  return suggestions
})

// 医疗事件模板
const medicalTemplates = {
  fall: {
    eventType: 'fall',
    template: {
      plan: {
        objectives: [
          '降低患者跌倒发生率',
          '提高跌倒风险识别准确性',
          '完善跌倒预防措施'
        ],
        methods: [
          '完善跌倒风险评估工具',
          '加强高危患者监护',
          '改善环境安全设施'
        ],
        smartSuggestions: [
          '建议使用Morse跌倒风险评估量表',
          '考虑增加床边护理频次',
          '评估患者活动能力和认知状态'
        ]
      },
      do: {
        actions: [
          '实施跌倒风险评估',
          '加强患者安全教育',
          '改善病房环境'
        ],
        resources: [
          '护理人员培训',
          '安全设施改进',
          '评估工具配备'
        ],
        smartSuggestions: [
          '确保床栏高度适当',
          '保持地面干燥无障碍物',
          '提供防滑鞋具'
        ]
      },
      check: {
        metrics: [
          '跌倒发生率',
          '风险评估准确率',
          '预防措施执行率'
        ],
        smartSuggestions: [
          '建立跌倒事件报告系统',
          '定期统计分析跌倒数据',
          '评估预防措施有效性'
        ]
      },
      act: {
        improvements: [
          '优化风险评估流程',
          '完善预防措施标准',
          '加强人员培训'
        ],
        smartSuggestions: [
          '制定跌倒预防标准操作程序',
          '建立持续改进机制',
          '分享最佳实践经验'
        ]
      }
    }
  },
  medication: {
    eventType: 'medication',
    template: {
      plan: {
        smartSuggestions: [
          '建立用药错误报告系统',
          '完善用药核查流程',
          '加强药师参与临床用药'
        ]
      },
      do: {
        smartSuggestions: [
          '实施用药双核查制度',
          '使用条码扫描技术',
          '加强患者用药教育'
        ]
      },
      check: {
        smartSuggestions: [
          '统计用药错误发生率',
          '分析错误类型和原因',
          '评估干预措施效果'
        ]
      },
      act: {
        smartSuggestions: [
          '优化用药流程标准',
          '完善信息系统功能',
          '建立用药安全文化'
        ]
      }
    }
  }
  // 可以继续添加其他模板...
}

// 工具函数
function getTemplateByType(type: string) {
  return medicalTemplates[type] || null
}

function getStepStatus(index: number) {
  if (index < currentStepIndex.value) return 'finish'
  if (index === currentStepIndex.value) return 'process'
  return 'wait'
}

function getStepPosition(index: number) {
  const angle = (index * 90) - 45 // 从右上角开始，顺时针
  const radius = 60
  const x = Math.cos(angle * Math.PI / 180) * radius
  const y = Math.sin(angle * Math.PI / 180) * radius

  return {
    transform: `translate(${x}px, ${y}px)`
  }
}

function isStepCompleted(stepKey: string) {
  return localData[stepKey].status === 'completed'
}

function getPhaseCompletion(phase: string) {
  const phaseData = localData[phase]
  const fields = currentPhaseInfo.value.fields.filter(f => f.required)
  let completedFields = 0

  fields.forEach(field => {
    if (phaseData[field.key] && phaseData[field.key].trim()) {
      completedFields++
    }
  })

  return Math.round((completedFields / fields.length) * 100)
}

function isCheckItemCompleted(item: string) {
  // 简单的完成度检查逻辑
  const phase = currentStep.value
  const completion = getPhaseCompletion(phase)
  return completion > 60 // 假设60%以上算完成
}

function getProgressColor() {
  if (completionPercentage.value >= 80) return '#52c41a'
  if (completionPercentage.value >= 50) return '#1890ff'
  return '#faad14'
}

function formatTime(date: Date) {
  return dayjs(date).format('HH:mm')
}

// 事件处理函数
function handleStepChange(index: number) {
  if (!props.readonly) {
    currentStep.value = steps[index].key as any
  }
}

function switchToStep(stepKey: string) {
  if (!props.readonly) {
    currentStep.value = stepKey as any
  }
}

function handleTemplateChange(templateType: string) {
  if (!templateType) return

  const template = getTemplateByType(templateType)
  if (template) {
    // 应用模板建议到智能提示中
    Object.keys(template.template).forEach(phase => {
      const phaseTemplate = template.template[phase]
      const phaseInfo = phaseInfoMap[phase]

      if (phaseInfo && phaseTemplate.smartSuggestions) {
        phaseInfo.fields.forEach(field => {
          field.smartSuggestions = phaseTemplate.smartSuggestions || []
        })
      }
    })

    message.success(`已应用${templateType === 'fall' ? '跌倒事件' : '医疗事件'}模板`)
  }
}

function handleFieldChange(fieldKey: string) {
  handleDataChange()
}

function handleStatusChange() {
  handleDataChange()
}

function handleDataChange() {
  emit('data-change', { ...localData })

  // 触发自动保存
  if (!props.readonly) {
    autoSave()
  }
}

function handlePrevStep() {
  if (currentStepIndex.value > 0) {
    currentStep.value = steps[currentStepIndex.value - 1].key as any
  }
}

function handleNextStep() {
  if (currentStepIndex.value < steps.length - 1) {
    currentStep.value = steps[currentStepIndex.value + 1].key as any
  } else {
    // 完成分析
    handleCompleteAnalysis()
  }
}

function handleCompleteAnalysis() {
  Modal.confirm({
    title: '完成PDCA分析',
    content: '确认完成当前PDCA分析吗？完成后可以导出分析结果。',
    okText: '确认完成',
    cancelText: '继续编辑',
    onOk: () => {
      // 标记所有阶段为完成
      Object.keys(localData).forEach(key => {
        if (key !== 'title' && localData[key].status) {
          localData[key].status = 'completed'
        }
      })

      handleDataChange()
      message.success('PDCA分析已完成！')

      // 可以触发导出或其他后续操作
      handleExport()
    }
  })
}

async function handleReset() {
  try {
    isResetting.value = true

    await new Promise((resolve, reject) => {
      Modal.confirm({
        title: '确认重置',
        content: '重置将清空所有PDCA分析数据，此操作不可恢复。确定要继续吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: resolve,
        onCancel: reject
      })
    })

    Object.assign(localData, {
      title: '',
      plan: { status: 'pending', responsible: '', deadline: null, objectives: '', methods: '', timeline: '' },
      do: { status: 'pending', responsible: '', deadline: null, actions: '', resources: '', progress: '' },
      check: { status: 'pending', responsible: '', deadline: null, metrics: '', results: '', evaluation: '' },
      act: { status: 'pending', responsible: '', deadline: null, improvements: '', standardization: '', nextCycle: '' }
    })

    currentStep.value = 'plan'
    selectedTemplate.value = ''

    message.success('PDCA分析已重置')
    handleDataChange()
  } catch (error) {
    // 用户取消重置
  } finally {
    isResetting.value = false
  }
}

async function handleExport() {
  try {
    isExporting.value = true
    console.log('🔄 开始导出PDCA数据 - EnhancedPDCAAnalysis')
    console.log('📋 localData keys:', Object.keys(localData))
    console.log('📋 phaseInfoMap keys:', Object.keys(phaseInfoMap))

    // 只处理PDCA的四个核心阶段，过滤掉无关字段
    const validPhases = ['plan', 'do', 'check', 'act']
    console.log('📋 要处理的有效phases:', validPhases)

    // 构建完整的导出数据结构
    const exportData = {
      title: localData.title && localData.title !== 'undefined - PDCA分析'
        ? localData.title
        : '事件PDCA分析报告',
      completionPercentage: completionPercentage.value,
      phases: validPhases.map(phase => {
        console.log(`📋 处理phase: ${phase}`)
        const phaseInfo = phaseInfoMap[phase]
        const phaseData = localData[phase] || {}

        if (!phaseInfo) {
          console.warn(`⚠️ phaseInfoMap中没有找到phase: ${phase}`)
          return {
            phase,
            title: `${phase} - 未知阶段`,
            status: phaseData.status || '未开始',
            responsible: phaseData.responsible || '',
            deadline: phaseData.deadline || '',
            content: phaseData
          }
        }

        // 构建完整的阶段数据，包含具体的分析内容
        const phaseContent = {
          ...phaseData,
          // 确保包含阶段的具体分析内容
          items: phaseData.items || [],
          description: phaseInfo.description || '',
          requirements: phaseInfo.requirements || []
        }

        return {
          phase,
          title: phaseInfo.title,
          description: phaseInfo.description,
          status: phaseData.status || '未开始',
          responsible: phaseData.responsible || '',
          deadline: phaseData.deadline || '',
          content: phaseContent,
          // 添加阶段完成度信息
          progress: phaseData.progress || 0,
          completedItems: phaseData.items?.filter(item => item.completed)?.length || 0,
          totalItems: phaseData.items?.length || 0
        }
      }),
      exportTime: new Date().toISOString(),
      template: selectedTemplate.value,
      // 添加元数据信息
      metadata: {
        version: '1.0.0',
        exportedBy: '系统用户', // 可以从用户信息中获取
        analysisType: 'PDCA循环分析',
        eventId: props.eventId || '',
        analysisId: props.analysisId || ''
      }
    }

    console.log('📤 准备emit导出数据:', exportData)
    emit('export', exportData)
    message.success('PDCA分析结果已导出')
  } catch (error) {
    console.error('❌ 导出过程中发生错误:', error)
    message.error('导出失败，请重试')
  } finally {
    isExporting.value = false
  }
}

// 处理导出菜单点击事件
async function handleExportMenuClick({ key }: { key: string }) {
  try {
    isExporting.value = true
    console.log(`🔄 开始导出PDCA数据为${key}格式 - EnhancedPDCAAnalysis`)

    // 如果没有标题，自动填充
    if (!localData.title || localData.title === 'undefined - PDCA分析') {
      const eventName = props.eventInfo?.evtname || props.eventInfo?.evName || '患者跌倒事件'
      localData.title = `${eventName} - PDCA分析`
      handleDataChange()
    }

    // 只处理PDCA的四个核心阶段，过滤掉无关字段
    const validPhases = ['plan', 'do', 'check', 'act']

    // 构建标准导出数据结构
    const standardExportData: ExportData = {
      title: localData.title || '事件PDCA分析报告',
      eventId: props.eventId,
      analysisId: props.analysisId,
      exportTime: new Date().toISOString(),
      completionPercentage: completionPercentage.value,
      phases: validPhases.map(phase => {
        const phaseInfo = phaseInfoMap[phase]
        const phaseData = localData[phase] || {}

        if (!phaseInfo) {
          return {
            phase,
            title: `${phase} - 未知阶段`,
            description: '',
            status: phaseData.status || '未开始',
            responsible: phaseData.responsible || '',
            deadline: phaseData.deadline || '',
            content: phaseData,
            progress: 0,
            completedItems: 0,
            totalItems: 0
          }
        }

        // 构建完整的阶段数据
        const phaseContent = {
          ...phaseData,
          items: phaseData.items || [],
          description: phaseInfo.description || '',
          requirements: phaseInfo.requirements || []
        }

        return {
          phase,
          title: phaseInfo.title,
          description: phaseInfo.description,
          status: phaseData.status || '未开始',
          responsible: phaseData.responsible || '',
          deadline: phaseData.deadline || '',
          content: phaseContent,
          progress: phaseData.progress || 0,
          completedItems: phaseData.items?.filter(item => item.completed)?.length || 0,
          totalItems: phaseData.items?.length || 0
        }
      }),
      metadata: {
        version: '1.0.0',
        exportedBy: '系统用户',
        analysisType: 'PDCA循环分析',
        eventId: props.eventId || '',
        analysisId: props.analysisId || ''
      }
    }

    // 获取事件类型
    const eventType = props.eventInfo?.evtname || props.eventInfo?.evName || '患者跌倒'

    // 根据选择的格式进行导出
    let format: ExportFormat
    switch (key) {
      case 'excel':
        format = ExportFormat.EXCEL
        break
      case 'word':
        format = ExportFormat.WORD
        break
      case 'pdf':
        format = ExportFormat.PDF
        break
      default:
        format = ExportFormat.EXCEL
    }

    // 发出多格式导出事件，让父组件处理
    emit('export-format', standardExportData, format)

    console.log(`✅ ${format}格式导出事件已发出`)
  } catch (error) {
    console.error(`❌ ${key}格式导出失败:`, error)
    message.error(`${key}格式导出失败：${error.message || '未知错误'}`)
  } finally {
    isExporting.value = false
  }
}

function showFieldSuggestions(field: any) {
  Modal.info({
    title: `${field.label} - 智能建议`,
    width: 600,
    content: h('div', [
      h('p', '以下是基于医疗最佳实践的建议：'),
      h('ul', field.smartSuggestions.map(suggestion =>
        h('li', { style: 'margin-bottom: 8px; line-height: 1.5;' }, suggestion)
      ))
    ])
  })
}

function showHelp() {
  Modal.info({
    title: '增强版PDCA分析工具使用帮助',
    width: 700,
    content: h('div', { style: 'line-height: 1.6;' }, [
      h('h4', 'PDCA循环分析法'),
      h('p', 'PDCA是一个持续改进的循环过程，本工具针对医疗场景进行了优化：'),

      h('h5', '🚀 新功能特性'),
      h('ul', [
        h('li', '智能模板：提供跌倒、用药错误等医疗事件专用模板'),
        h('li', '步骤导航：清晰的进度指示和步骤切换'),
        h('li', '智能建议：基于医疗最佳实践的专业建议'),
        h('li', '实时保存：自动保存分析内容，避免数据丢失'),
        h('li', '完成度跟踪：实时显示各阶段完成情况')
      ]),

      h('h5', '📋 使用步骤'),
      h('ol', [
        h('li', '选择合适的医疗事件模板（可选）'),
        h('li', '按照Plan→Do→Check→Act的顺序填写内容'),
        h('li', '利用智能建议完善分析内容'),
        h('li', '设置责任人和截止时间'),
        h('li', '更新阶段状态并导出结果')
      ]),

      h('h5', '💡 使用技巧'),
      h('ul', [
        h('li', '点击步骤导航或循环图可快速切换阶段'),
        h('li', '查看智能建议获取专业指导'),
        h('li', '利用模板快速开始分析'),
        h('li', '定期更新阶段状态跟踪进度')
      ])
    ])
  })
}

// 自动保存功能
let autoSaveTimer: NodeJS.Timeout | null = null

function autoSave() {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }

  autoSaveTimer = setTimeout(async () => {
    try {
      isSaving.value = true

      // 调用真实的保存API
      await handleSave()
      lastSaved.value = new Date()
    } catch (error) {
      console.error('自动保存失败:', error)
    } finally {
      isSaving.value = false
    }
  }, 30000) // 30秒后自动保存
}

// 手动保存功能
async function handleSave() {
  try {
    if (!props.eventInfo?.id) {
      throw new Error('事件ID不能为空')
    }

    const saveRequest = {
      eventId: props.eventInfo.id,
      analysisId: props.eventInfo.analysisId || '',
      toolType: 'pdca',
      toolName: 'PDCA循环分析',
      toolData: { ...localData },
      autoSave: true,
      mainProblem: localData.title || '',
      conclusion: getAnalysisConclusion()
    }

    const result = await savePDCAData(saveRequest)

    if (result) {
      emit('save', { ...localData })
      console.log('PDCA数据保存成功')
    }
  } catch (error) {
    console.error('保存PDCA数据失败:', error)
    throw error
  }
}

// 获取分析结论
function getAnalysisConclusion(): string {
  const conclusions = []

  if (localData.plan?.objectives) {
    conclusions.push(`计划目标：${localData.plan.objectives}`)
  }
  if (localData.act?.improvements) {
    conclusions.push(`改进措施：${localData.act.improvements}`)
  }

  return conclusions.join('；')
}

// 加载PDCA数据
async function loadPDCAData() {
  try {
    if (!props.eventInfo?.id) {
      return
    }

    const params = {
      eventId: props.eventInfo.id,
      analysisId: props.eventInfo.analysisId || ''
    }

    const result = await getPDCAData(params)

    if (result && result.toolData) {
      Object.assign(localData, result.toolData)
      console.log('PDCA数据加载成功')
    }
  } catch (error) {
    console.error('加载PDCA数据失败:', error)
    // 加载失败时使用默认数据，不显示错误信息
  }
}

// 加载医疗模板
async function loadMedicalTemplate(eventType: string) {
  try {
    const template = await getPDCATemplateByEventType(eventType)

    if (template && template.template) {
      Object.assign(localData, template.template)
      selectedTemplate.value = template.name || eventType
      handleDataChange()
      message.success(`已应用${template.name || eventType}模板`)
    }
  } catch (error) {
    console.error('加载医疗模板失败:', error)
    message.error('加载模板失败，请重试')
  }
}

// 获取智能建议
async function loadSmartSuggestions(eventType: string, phase: string) {
  try {
    const suggestions = await getPDCASmartSuggestions(eventType, phase)

    if (suggestions && suggestions.length > 0) {
      return suggestions
    }
  } catch (error) {
    console.error('获取智能建议失败:', error)
  }

  return []
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadPDCAData()

  // 如果有事件类型，尝试加载对应模板
  if (props.eventInfo?.evtType && !localData.title) {
    await loadMedicalTemplate(props.eventInfo.evtType)
  }
})

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(localData, newData)
  }
}, { deep: true, immediate: true })

// 监听事件信息变化，自动填充相关内容
watch(() => props.eventInfo, async (eventInfo) => {
  if (eventInfo) {
    // 加载已保存的PDCA数据
    await loadPDCAData()

    // 如果没有标题，自动填充
    if (!localData.title) {
      const eventName = eventInfo.evtname || eventInfo.evName || '患者跌倒事件'
      localData.title = `${eventName} - PDCA分析`
      handleDataChange()
    }
  }
}, { immediate: true })
</script>

<style scoped lang="less">
.enhanced-pdca-analysis {
  .analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    margin-bottom: 16px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 16px;

      .progress-indicator {
        display: flex;
        align-items: center;
        gap: 8px;

        .progress-text {
          font-size: 12px;
          color: #666;
          white-space: nowrap;
        }
      }
    }
  }

  .steps-navigation {
    margin-bottom: 24px;

    :deep(.ant-steps) {
      .ant-steps-item {
        cursor: pointer;

        &:hover {
          .ant-steps-item-title {
            color: #1890ff;
          }
        }
      }

      .step-icon {
        font-size: 16px;
      }
    }
  }

  .main-workspace {
    display: flex;
    gap: 24px;
    min-height: 500px;

    .left-panel {
      width: 300px;
      flex-shrink: 0;

      .compact-cycle {
        margin-bottom: 16px;

        .cycle-container {
          position: relative;
          width: 200px;
          height: 200px;
          margin: 0 auto;
          border: 2px solid #e8e8e8;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          .cycle-center {
            text-align: center;
            z-index: 10;

            .center-title {
              font-size: 24px;
              font-weight: bold;
              color: #1890ff;
            }

            .center-subtitle {
              font-size: 12px;
              color: #666;
              margin-top: 4px;
            }
          }

          .cycle-step {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #f5f5f5;
            border: 2px solid #d9d9d9;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              transform: scale(1.1);
              border-color: #1890ff;
            }

            &.active {
              background: #1890ff;
              border-color: #1890ff;
              color: white;
              transform: scale(1.1);
            }

            &.completed {
              background: #52c41a;
              border-color: #52c41a;
              color: white;
            }

            .step-content {
              text-align: center;

              .step-icon {
                font-size: 16px;
                margin-bottom: 2px;
              }

              .step-label {
                font-size: 10px;
                line-height: 1;
              }
            }
          }
        }
      }

      .phase-info-card {
        :deep(.ant-card) {
          .ant-card-head {
            padding: 12px 16px;

            .ant-card-head-title {
              font-size: 14px;
              font-weight: 600;
            }
          }

          .ant-card-body {
            padding: 16px;
          }
        }

        .phase-description {
          color: #666;
          margin-bottom: 16px;
          line-height: 1.5;
        }

        .smart-suggestions {
          margin-bottom: 16px;
          padding: 12px;
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          border-radius: 4px;

          h5 {
            margin: 0 0 8px 0;
            color: #52c41a;
            font-size: 13px;
          }

          ul {
            margin: 0;
            padding-left: 16px;

            li {
              font-size: 12px;
              line-height: 1.4;
              margin-bottom: 4px;
              color: #666;
            }
          }
        }

        .completion-check {
          .check-items {
            margin-top: 8px;

            .check-item {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 12px;
              color: #666;
              margin-bottom: 4px;

              &.completed {
                color: #52c41a;
              }

              .anticon {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    .right-panel {
      flex: 1;

      .smart-form-container {
        :deep(.ant-card) {
          .ant-card-head {
            .ant-card-head-title {
              font-size: 16px;
              font-weight: 600;
            }
          }
        }

        .field-suggestions {
          margin-top: 4px;
        }

        :deep(.ant-form-item) {
          margin-bottom: 16px;

          .ant-form-item-label {
            label {
              font-weight: 500;
            }
          }

          .ant-form-item-explain {
            font-size: 12px;
          }
        }
      }
    }
  }

  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    margin-top: 24px;

    .action-left {
      .auto-save-status {
        display: flex;
        align-items: center;
        gap: 6px;

        .save-text {
          font-size: 12px;
          color: #666;
        }
      }
    }

    .action-right {
      // 样式已由 a-space 处理
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .enhanced-pdca-analysis {
    .main-workspace {
      flex-direction: column;

      .left-panel {
        width: 100%;

        .compact-cycle {
          .cycle-container {
            width: 150px;
            height: 150px;

            .cycle-step {
              width: 45px;
              height: 45px;

              .step-content {
                .step-icon {
                  font-size: 14px;
                }

                .step-label {
                  font-size: 9px;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .enhanced-pdca-analysis {
    .analysis-header {
      flex-direction: column;
      gap: 12px;

      .header-left {
        width: 100%;
        flex-direction: column;
        gap: 12px;

        :deep(.ant-input) {
          width: 100% !important;
        }

        :deep(.ant-select) {
          width: 100% !important;
        }
      }

      .header-right {
        width: 100%;
        justify-content: space-between;
      }
    }

    .main-workspace {
      .left-panel {
        .compact-cycle {
          display: none; // 在小屏幕上隐藏循环图
        }
      }
    }

    .action-bar {
      flex-direction: column;
      gap: 12px;

      .action-left,
      .action-right {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style>
