package com.elkj.nms.module.evt.dal.mysql.analyze;

import com.elkj.nms.framework.common.pojo.PageResult;
import com.elkj.nms.framework.mybatis.core.mapper.BaseMapperX;
import com.elkj.nms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.elkj.nms.module.evt.dal.dataobject.analyze.AnalyzeToolDataDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 分析工具数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AnalyzeToolDataMapper extends BaseMapperX<AnalyzeToolDataDO> {

    /**
     * 根据事件ID查询工具数据
     */
    default List<AnalyzeToolDataDO> selectListByEventId(String eventId) {
        return selectList(new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                .eq(AnalyzeToolDataDO::getEventId, eventId)
                .eq(AnalyzeToolDataDO::getIsActive, "1")
                .orderByDesc(AnalyzeToolDataDO::getCreateTime));
    }

    /**
     * 根据分析ID查询工具数据
     */
    default List<AnalyzeToolDataDO> selectListByAnalysisId(String analysisId) {
        return selectList(new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                .eq(AnalyzeToolDataDO::getAnalysisId, analysisId)
                .eq(AnalyzeToolDataDO::getIsActive, "1")
                .orderByDesc(AnalyzeToolDataDO::getCreateTime));
    }

    /**
     * 根据工具类型查询数据
     */
    default List<AnalyzeToolDataDO> selectListByToolType(String toolType, String eventId) {
        return selectList(new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                .eq(AnalyzeToolDataDO::getToolType, toolType)
                .eq(AnalyzeToolDataDO::getEventId, eventId)
                .eq(AnalyzeToolDataDO::getIsActive, "1")
                .orderByDesc(AnalyzeToolDataDO::getVersionNo));
    }

    /**
     * 获取最新版本的工具数据
     */
    default AnalyzeToolDataDO selectLatestByToolTypeAndEvent(String toolType, String eventId) {
        return selectOne(new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                .eq(AnalyzeToolDataDO::getToolType, toolType)
                .eq(AnalyzeToolDataDO::getEventId, eventId)
                .eq(AnalyzeToolDataDO::getIsActive, "1")
                .orderByDesc(AnalyzeToolDataDO::getVersionNo)
                .last("LIMIT 1"));
    }
} 