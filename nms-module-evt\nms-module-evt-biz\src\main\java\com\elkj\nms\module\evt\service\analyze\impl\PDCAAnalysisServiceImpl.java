package com.elkj.nms.module.evt.service.analyze.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.elkj.nms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.elkj.nms.framework.common.pojo.PageResult;
import com.elkj.nms.framework.common.util.json.JsonUtils;
import com.elkj.nms.framework.common.util.object.BeanUtils;
import com.elkj.nms.framework.security.core.util.SecurityFrameworkUtils;
import com.elkj.nms.module.evt.controller.admin.analyze.vo.PDCAAnalysisVO;
import com.elkj.nms.module.evt.dal.dataobject.analyze.AnalyzeToolDataDO;
import com.elkj.nms.module.evt.dal.dataobject.info.InfoDO;
import com.elkj.nms.module.evt.dal.mysql.analyze.AnalyzeToolDataMapper;
import com.elkj.nms.module.evt.dal.mysql.info.InfoMapper;
import com.elkj.nms.module.evt.service.analyze.KnowledgeBaseService;
import com.elkj.nms.module.evt.service.analyze.PDCAAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PDCA分析管理 Service 实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Service
public class PDCAAnalysisServiceImpl implements PDCAAnalysisService {

    @Resource
    private AnalyzeToolDataMapper analyzeToolDataMapper;

    @Resource
    private InfoMapper infoMapper;

    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    private static final String TOOL_TYPE_PDCA = "pdca";
    private static final String STATUS_DRAFT = "draft";
    private static final String STATUS_IN_PROGRESS = "in_progress";
    private static final String STATUS_COMPLETED = "completed";

    @Override
    @Transactional
    public String savePDCAAnalysis(PDCAAnalysisVO.SaveReqVO request) {
        try {
            // 检查是否已存在PDCA数据
            LambdaQueryWrapper<AnalyzeToolDataDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AnalyzeToolDataDO::getEventId, request.getEventId())
                       .eq(AnalyzeToolDataDO::getToolType, TOOL_TYPE_PDCA);

            if (StringUtils.hasText(request.getAnalysisId())) {
                queryWrapper.eq(AnalyzeToolDataDO::getAnalysisId, request.getAnalysisId());
            }

            // 按创建时间倒序排列，选择最新的一条记录
            queryWrapper.orderByDesc(AnalyzeToolDataDO::getCreateTime);

            List<AnalyzeToolDataDO> existingDataList = analyzeToolDataMapper.selectList(queryWrapper);
            AnalyzeToolDataDO existingData = existingDataList.isEmpty() ? null : existingDataList.get(0);

            if (existingData != null) {
                // 更新现有数据
                existingData.setToolData(JsonUtils.toJsonString(request.getToolData()));
                existingData.setMainProblem(request.getMainProblem());
                existingData.setConclusion(request.getConclusion());

                analyzeToolDataMapper.updateById(existingData);

                log.info("更新PDCA分析数据成功，id: {}", existingData.getId());
                return existingData.getId();
            } else {
                // 创建新数据
                AnalyzeToolDataDO newData = new AnalyzeToolDataDO();
                newData.setId("PDCA" + System.currentTimeMillis()); // 生成唯一ID
                newData.setEventId(request.getEventId());
                newData.setAnalysisId(StringUtils.hasText(request.getAnalysisId()) ?
                                     request.getAnalysisId() : null);
                newData.setToolType(TOOL_TYPE_PDCA);
                newData.setToolName(request.getToolName());
                newData.setToolData(JsonUtils.toJsonString(request.getToolData()));
                newData.setMainProblem(request.getMainProblem());
                newData.setConclusion(request.getConclusion());
                newData.setCreatorUserId(SecurityFrameworkUtils.getLoginUserId());
                newData.setVersionNo(1);
                newData.setIsActive("1");

                analyzeToolDataMapper.insert(newData);

                log.info("创建PDCA分析数据成功，id: {}", newData.getId());
                return newData.getId();
            }
        } catch (Exception e) {
            log.error("保存PDCA分析数据失败", e);
            throw new RuntimeException("保存PDCA分析数据失败: " + e.getMessage());
        }
    }

    @Override
    public PDCAAnalysisVO.RespVO getPDCAAnalysis(String eventId, String analysisId) {
        try {
            LambdaQueryWrapper<AnalyzeToolDataDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AnalyzeToolDataDO::getEventId, eventId)
                       .eq(AnalyzeToolDataDO::getToolType, TOOL_TYPE_PDCA)
                       .eq(AnalyzeToolDataDO::getIsActive, "1");
            
            if (StringUtils.hasText(analysisId)) {
                queryWrapper.eq(AnalyzeToolDataDO::getAnalysisId, analysisId);
            }

            queryWrapper.orderByDesc(AnalyzeToolDataDO::getCreateTime);

            // 使用selectList避免TooManyResultsException，选择最新的一条记录
            List<AnalyzeToolDataDO> toolDataList = analyzeToolDataMapper.selectList(queryWrapper);
            AnalyzeToolDataDO toolData = toolDataList.isEmpty() ? null : toolDataList.get(0);
            
            if (toolData == null) {
                log.info("未找到PDCA分析数据，eventId: {}, analysisId: {}", eventId, analysisId);
                return null;
            }

            PDCAAnalysisVO.RespVO result = BeanUtils.toBean(toolData, PDCAAnalysisVO.RespVO.class);
            result.setId(toolData.getId());
            result.setEventId(toolData.getEventId());
            result.setAnalysisId(toolData.getAnalysisId());
            
            // 解析JSON数据
            if (StringUtils.hasText(toolData.getToolData())) {
                Map<String, Object> toolDataMap = JsonUtils.parseObject(toolData.getToolData(), Map.class);
                result.setToolData(toolDataMap);
            }

            log.info("获取PDCA分析数据成功，id: {}", result.getId());
            return result;
            
        } catch (Exception e) {
            log.error("获取PDCA分析数据失败，eventId: {}", eventId, e);
            throw new RuntimeException("获取PDCA分析数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void updatePDCAAnalysis(String id, PDCAAnalysisVO.UpdateReqVO request) {
        try {
            AnalyzeToolDataDO toolData = analyzeToolDataMapper.selectOne(
                new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                    .eq(AnalyzeToolDataDO::getId, id)
            );
            if (toolData == null) {
                throw new RuntimeException("PDCA分析数据不存在");
            }

            toolData.setToolData(JsonUtils.toJsonString(request.getToolData()));
            toolData.setMainProblem(request.getMainProblem());
            toolData.setConclusion(request.getConclusion());
            toolData.setUpdateTime(LocalDateTime.now());

            analyzeToolDataMapper.update(toolData,
                new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                    .eq(AnalyzeToolDataDO::getId, id)
            );
            
            log.info("更新PDCA分析数据成功，id: {}", id);
            
        } catch (Exception e) {
            log.error("更新PDCA分析数据失败，id: {}", id, e);
            throw new RuntimeException("更新PDCA分析数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void deletePDCAAnalysis(String id) {
        try {
            AnalyzeToolDataDO toolData = analyzeToolDataMapper.selectOne(
                new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                    .eq(AnalyzeToolDataDO::getId, id)
            );
            if (toolData == null) {
                throw new RuntimeException("PDCA分析数据不存在");
            }

            // 软删除
            toolData.setIsActive("0");
            toolData.setUpdateTime(LocalDateTime.now());
            analyzeToolDataMapper.update(toolData,
                new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                    .eq(AnalyzeToolDataDO::getId, id)
            );
            
            log.info("删除PDCA分析数据成功，id: {}", id);
            
        } catch (Exception e) {
            log.error("删除PDCA分析数据失败，id: {}", id, e);
            throw new RuntimeException("删除PDCA分析数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void completePDCAAnalysis(String id) {
        try {
            AnalyzeToolDataDO toolData = analyzeToolDataMapper.selectOne(
                new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                    .eq(AnalyzeToolDataDO::getId, id)
            );
            if (toolData == null) {
                throw new RuntimeException("PDCA分析数据不存在");
            }

            // 更新状态为已完成
            Map<String, Object> toolDataMap = JsonUtils.parseObject(toolData.getToolData(), Map.class);
            toolDataMap.put("status", STATUS_COMPLETED);
            toolDataMap.put("completedTime", LocalDateTime.now().toString());
            
            toolData.setToolData(JsonUtils.toJsonString(toolDataMap));
            toolData.setUpdateTime(LocalDateTime.now());
            analyzeToolDataMapper.update(toolData,
                new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                    .eq(AnalyzeToolDataDO::getId, id)
            );
            
            log.info("完成PDCA分析成功，id: {}", id);
            
        } catch (Exception e) {
            log.error("完成PDCA分析失败，id: {}", id, e);
            throw new RuntimeException("完成PDCA分析失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> exportPDCAAnalysis(String id) {
        try {
            AnalyzeToolDataDO toolData = analyzeToolDataMapper.selectOne(
                new LambdaQueryWrapperX<AnalyzeToolDataDO>()
                    .eq(AnalyzeToolDataDO::getId, id)
            );
            if (toolData == null) {
                throw new RuntimeException("PDCA分析数据不存在");
            }

            // 获取事件信息
            InfoDO eventInfo = infoMapper.selectById(toolData.getEventId());
            
            Map<String, Object> exportData = new HashMap<>();
            exportData.put("title", "事件PDCA分析报告");
            exportData.put("pdcaId", id);
            exportData.put("eventInfo", eventInfo);
            exportData.put("toolData", JsonUtils.parseObject(toolData.getToolData(), Map.class));
            exportData.put("mainProblem", toolData.getMainProblem());
            exportData.put("conclusion", toolData.getConclusion());
            exportData.put("exportTime", LocalDateTime.now());
            exportData.put("exportUser", SecurityFrameworkUtils.getLoginUserId());
            
            log.info("导出PDCA分析报告成功，id: {}", id);
            return exportData;
            
        } catch (Exception e) {
            log.error("导出PDCA分析报告失败，id: {}", id, e);
            throw new RuntimeException("导出PDCA分析报告失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getAllTemplates() {
        try {
            // 从知识库获取PDCA模板
            List<Map<String, Object>> templates = knowledgeBaseService.getKnowledgeToolTemplates(TOOL_TYPE_PDCA);
            
            // 添加内置医疗模板
            templates.addAll(getBuiltInMedicalTemplates());
            
            log.info("获取所有PDCA模板成功，数量: {}", templates.size());
            return templates;
            
        } catch (Exception e) {
            log.error("获取PDCA模板失败", e);
            throw new RuntimeException("获取PDCA模板失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getTemplateByEventType(String eventType) {
        try {
            // 获取内置医疗模板
            Map<String, Object> template = getBuiltInTemplateByEventType(eventType);
            
            if (template == null) {
                // 从知识库搜索相关模板
                List<Map<String, Object>> templates = knowledgeBaseService.searchKnowledge(
                    eventType, eventType, "tool", Arrays.asList(TOOL_TYPE_PDCA));
                
                if (!templates.isEmpty()) {
                    template = templates.get(0);
                }
            }
            
            log.info("获取特定类型PDCA模板成功，eventType: {}", eventType);
            return template;
            
        } catch (Exception e) {
            log.error("获取特定类型PDCA模板失败，eventType: {}", eventType, e);
            return null;
        }
    }

    @Override
    public List<String> getSmartSuggestions(String eventType, String phase) {
        try {
            List<String> suggestions = new ArrayList<>();
            
            // 获取内置建议
            suggestions.addAll(getBuiltInSmartSuggestions(eventType, phase));
            
            // 从知识库获取智能推荐
            List<Map<String, Object>> knowledgeList = knowledgeBaseService.intelligentRecommend(
                null, eventType, phase, 5);
            
            suggestions.addAll(knowledgeList.stream()
                .map(k -> k.get("title").toString())
                .collect(Collectors.toList()));
            
            log.info("获取智能建议成功，eventType: {}, phase: {}, 数量: {}", eventType, phase, suggestions.size());
            return suggestions;
            
        } catch (Exception e) {
            log.error("获取智能建议失败，eventType: {}, phase: {}", eventType, phase, e);
            return new ArrayList<>();
        }
    }

    @Override
    public PageResult<PDCAAnalysisVO.ListRespVO> getPDCAAnalysisList(Integer pageNum, Integer pageSize, 
                                                                     String eventType, String status) {
        try {
            Page<AnalyzeToolDataDO> page = new Page<>(pageNum, pageSize);
            
            LambdaQueryWrapper<AnalyzeToolDataDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AnalyzeToolDataDO::getToolType, TOOL_TYPE_PDCA)
                       .eq(AnalyzeToolDataDO::getIsActive, 1)
                       .orderByDesc(AnalyzeToolDataDO::getCreateTime);

            IPage<AnalyzeToolDataDO> result = analyzeToolDataMapper.selectPage(page, queryWrapper);
            
            List<PDCAAnalysisVO.ListRespVO> list = result.getRecords().stream()
                .map(this::convertToListRespVO)
                .collect(Collectors.toList());

            log.info("分页查询PDCA分析数据成功，总数: {}", result.getTotal());
            return new PageResult<>(list, result.getTotal());
            
        } catch (Exception e) {
            log.error("分页查询PDCA分析数据失败", e);
            throw new RuntimeException("分页查询PDCA分析数据失败: " + e.getMessage());
        }
    }

    // ========================== 私有方法 ==========================

    private List<Map<String, Object>> getBuiltInMedicalTemplates() {
        List<Map<String, Object>> templates = new ArrayList<>();
        
        // 跌倒事件模板
        Map<String, Object> fallTemplate = new HashMap<>();
        fallTemplate.put("eventType", "fall");
        fallTemplate.put("name", "患者跌倒事件PDCA分析");
        fallTemplate.put("description", "针对患者跌倒事件的标准化PDCA分析模板");
        templates.add(fallTemplate);
        
        // 用药错误模板
        Map<String, Object> medicationTemplate = new HashMap<>();
        medicationTemplate.put("eventType", "medication");
        medicationTemplate.put("name", "用药错误事件PDCA分析");
        medicationTemplate.put("description", "针对用药错误事件的标准化PDCA分析模板");
        templates.add(medicationTemplate);
        
        // 感染事件模板
        Map<String, Object> infectionTemplate = new HashMap<>();
        infectionTemplate.put("eventType", "infection");
        infectionTemplate.put("name", "医院感染事件PDCA分析");
        infectionTemplate.put("description", "针对医院感染事件的标准化PDCA分析模板");
        templates.add(infectionTemplate);
        
        return templates;
    }

    private Map<String, Object> getBuiltInTemplateByEventType(String eventType) {
        // 这里可以根据事件类型返回对应的内置模板
        // 实际实现中可以从配置文件或数据库读取
        return null;
    }

    private List<String> getBuiltInSmartSuggestions(String eventType, String phase) {
        List<String> suggestions = new ArrayList<>();
        
        // 根据事件类型和阶段提供内置建议
        if ("fall".equals(eventType)) {
            if ("plan".equals(phase)) {
                suggestions.add("建议使用Morse跌倒风险评估量表");
                suggestions.add("考虑增加床边护理频次");
                suggestions.add("评估患者活动能力和认知状态");
            }
        }
        
        return suggestions;
    }

    private PDCAAnalysisVO.ListRespVO convertToListRespVO(AnalyzeToolDataDO toolData) {
        PDCAAnalysisVO.ListRespVO vo = new PDCAAnalysisVO.ListRespVO();
        vo.setId(toolData.getId().toString());
        vo.setEventId(toolData.getEventId().toString());
        vo.setToolName(toolData.getToolName());
        vo.setMainProblem(toolData.getMainProblem());
        vo.setStatus(STATUS_DRAFT); // 默认状态
        vo.setProgress(0); // 默认进度
        vo.setCreateTime(toolData.getCreateTime());
        vo.setUpdateTime(toolData.getUpdateTime());
        
        // 获取事件信息
        InfoDO eventInfo = infoMapper.selectById(toolData.getEventId());
        if (eventInfo != null) {
            vo.setEventName(eventInfo.getEvtname());
            vo.setEventType(eventInfo.getEvtType());
        }
        
        return vo;
    }

    // 其他方法的实现将在后续添加...
    @Override
    public Map<String, Object> recommendTemplate(String eventId, String eventType, String eventName) {
        // TODO: 实现模板推荐逻辑
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getPDCAStatistics(String eventType, String startDate, String endDate) {
        // TODO: 实现统计功能
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> batchImportTemplates(List<Map<String, Object>> templates) {
        // TODO: 实现批量导入功能
        return new HashMap<>();
    }

    @Override
    public List<PDCAAnalysisVO.RespVO> getPDCAAnalysisHistory(String eventId) {
        // TODO: 实现历史版本查询
        return new ArrayList<>();
    }

    @Override
    public String copyPDCAAnalysis(String sourceId, String targetEventId) {
        // TODO: 实现复制功能
        return "";
    }

    @Override
    public Map<String, Object> validatePDCAData(Map<String, Object> pdcaData) {
        // TODO: 实现数据验证
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getPDCAPhaseCompletion(String id) {
        // TODO: 实现阶段完成情况查询
        return new HashMap<>();
    }

    @Override
    public void updatePDCAPhaseStatus(String id, String phase, String status) {
        // TODO: 实现阶段状态更新
    }

    @Override
    public Map<String, Object> getPDCAEffectivenessEvaluation(String id) {
        // TODO: 实现效果评估查询
        return new HashMap<>();
    }

    @Override
    public void savePDCAEffectivenessEvaluation(String id, Map<String, Object> evaluationData) {
        // TODO: 实现效果评估保存
    }
}
