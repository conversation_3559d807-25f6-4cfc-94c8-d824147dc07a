@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo      DLCOM Server Application Startup
echo ========================================
echo.

REM Change to project root directory
cd /d "%~dp0.."

REM Check if JAR file exists
if not exist "nms-server\target\nms-server.jar" (
    echo [ERROR] nms-server.jar not found!
    echo.
    echo Expected location: nms-server\target\nms-server.jar
    echo Current directory: %CD%
    echo.
    echo Please run compile.bat first to build the application.
    echo.
    pause
    exit /b 1
)

REM Check Java version
echo [INFO] Checking Java version...
java -version 2>&1 | findstr /i "version" >nul
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Java is not installed or not in PATH!
    echo.
    echo Please install Java 17 or higher and add it to your PATH.
    echo.
    pause
    exit /b 1
)

REM Display Java version
for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%g
    set JAVA_VERSION=!JAVA_VERSION:"=!
)
echo [INFO] Java Version: %JAVA_VERSION%

REM Set JVM parameters
set JVM_OPTS=-Xmx1024m -Xms512m -Dspring.profiles.active=local
set JVM_OPTS=%JVM_OPTS% -Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Djava.awt.headless=true
set JVM_OPTS=%JVM_OPTS% -Dspring.output.ansi.enabled=always

echo [INFO] JVM Options: %JVM_OPTS%
echo [INFO] JAR Location: nms-server\target\nms-server.jar
echo [INFO] Profile: local
echo.

REM Get current time
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "start_time=%YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%"

echo ========================================
echo [INFO] Starting nms Server...
echo [INFO] Start Time: %start_time%
echo ========================================
echo.
echo [INFO] Application will be available at:
echo         - Admin UI: http://localhost:48080
echo         - API Docs: http://localhost:48080/doc.html
echo.
echo [INFO] Press Ctrl+C to stop the server
echo.

REM Start the application
java %JVM_OPTS% -jar nms-server\target\nms-server.jar

REM If we reach here, the application has stopped
echo.
echo ========================================
echo [INFO] nms Server has stopped
echo ========================================

pause
