<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多格式导出功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
        }
        .error {
            background-color: #fff2f0;
            border-color: #ffccc7;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>DLCOM多格式导出功能测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>此页面用于测试PDCA分析报告的Excel、Word、PDF三种格式导出功能。</p>
        <p>点击下方按钮测试各种格式的导出功能。</p>
    </div>

    <div class="test-section">
        <h2>导出测试</h2>
        <button onclick="testExcelExport()">测试Excel导出</button>
        <button onclick="testWordExport()">测试Word导出</button>
        <button onclick="testPDFExport()">测试PDF导出</button>
        <div id="exportResult"></div>
    </div>

    <div class="test-section">
        <h2>测试数据预览</h2>
        <pre id="testDataPreview"></pre>
    </div>

    <script type="module">
        // 测试数据
        const testData = {
            eventInfo: {
                eventId: 'EVT2025070200000178',
                eventType: '患者跌倒',
                eventTitle: '患者跌倒事件分析',
                reportDate: '2025-08-03',
                reporter: '张护士',
                department: '内科病房'
            },
            pdcaData: {
                plan: {
                    problemDescription: '患者在病房内发生跌倒事件，需要分析原因并制定改进措施',
                    rootCause: '地面湿滑、患者行动不便、护理人员巡视不及时',
                    targetGoal: '降低患者跌倒发生率至0.5%以下',
                    actionPlan: '加强地面清洁、增加防滑措施、提高巡视频率',
                    timeline: '2025年8月-10月',
                    responsible: '护理部、后勤部',
                    resources: '防滑垫、警示标识、培训资源',
                    riskAssessment: '中等风险，需要持续监控'
                },
                do: {
                    implementationSteps: [
                        '购买并安装防滑垫',
                        '制作并张贴警示标识',
                        '组织护理人员培训',
                        '建立巡视记录制度'
                    ],
                    actualActions: '已完成防滑垫安装，正在进行人员培训',
                    timeline: '2025年8月1日-8月15日',
                    responsible: '护理部主任、病房护士长',
                    resourcesUsed: '防滑垫50个、警示标识20个、培训费用5000元',
                    challenges: '部分护理人员对新制度适应较慢'
                },
                check: {
                    monitoringMethod: '每日巡查、事件统计、患者反馈',
                    dataCollection: '跌倒事件记录表、巡视记录、患者满意度调查',
                    results: '跌倒事件减少60%，患者满意度提升',
                    analysis: '防滑措施效果显著，但仍需加强夜间巡视',
                    timeline: '2025年8月16日-8月31日',
                    responsible: '质控护士、病房护士长',
                    deviations: '夜间巡视频率仍需提高'
                },
                act: {
                    standardization: '将有效措施纳入标准操作流程',
                    improvements: '增加夜间巡视频率，完善应急预案',
                    nextCycle: '继续监控并优化防跌倒措施',
                    lessons: '预防措施的及时实施对降低风险至关重要',
                    timeline: '2025年9月1日起持续执行',
                    responsible: '护理部、各病房护士长',
                    followUp: '每月评估一次，季度总结改进'
                }
            },
            metadata: {
                analysisDate: '2025-08-03',
                analyst: '质控护士',
                reviewDate: '2025-08-10',
                reviewer: '护理部主任',
                version: 'V1.0',
                status: '已完成'
            }
        };

        // 显示测试数据
        document.getElementById('testDataPreview').textContent = JSON.stringify(testData, null, 2);

        // 导出测试函数
        window.testExcelExport = async function() {
            try {
                showResult('正在测试Excel导出...', false);
                
                // 动态导入xlsx库
                const { utils, writeFile } = await import('https://cdn.skypack.dev/xlsx');
                
                // 创建Excel数据
                const excelData = createExcelData(testData);
                const worksheet = utils.aoa_to_sheet(excelData);
                const workbook = {
                    SheetNames: ['PDCA分析报告'],
                    Sheets: { 'PDCA分析报告': worksheet }
                };
                
                // 生成文件名
                const filename = `PDCA分析报告_${testData.eventInfo.eventType}_${testData.eventInfo.eventId}_${testData.metadata.analysisDate}_${testData.metadata.version}.xlsx`;
                
                // 导出文件
                writeFile(workbook, filename);
                showResult('✅ Excel导出测试成功！文件已下载。', false);
            } catch (error) {
                showResult(`❌ Excel导出测试失败: ${error.message}`, true);
            }
        };

        window.testWordExport = function() {
            showResult('Word导出功能需要在Vue环境中测试，当前为简化测试环境。', false);
        };

        window.testPDFExport = function() {
            showResult('PDF导出功能需要在Vue环境中测试，当前为简化测试环境。', false);
        };

        function createExcelData(data) {
            const excelData = [];
            
            // 添加报告头部信息
            excelData.push(['PDCA分析报告']);
            excelData.push(['事件编号', data.eventInfo.eventId]);
            excelData.push(['事件类型', data.eventInfo.eventType]);
            excelData.push(['事件标题', data.eventInfo.eventTitle]);
            excelData.push(['报告日期', data.eventInfo.reportDate]);
            excelData.push(['报告人', data.eventInfo.reporter]);
            excelData.push(['科室', data.eventInfo.department]);
            excelData.push([]); // 空行

            // Plan阶段
            excelData.push(['Plan (计划阶段)']);
            excelData.push(['问题描述', data.pdcaData.plan.problemDescription]);
            excelData.push(['根本原因', data.pdcaData.plan.rootCause]);
            excelData.push(['目标设定', data.pdcaData.plan.targetGoal]);
            excelData.push(['行动计划', data.pdcaData.plan.actionPlan]);
            excelData.push(['时间安排', data.pdcaData.plan.timeline]);
            excelData.push(['负责人', data.pdcaData.plan.responsible]);
            excelData.push(['所需资源', data.pdcaData.plan.resources]);
            excelData.push(['风险评估', data.pdcaData.plan.riskAssessment]);
            excelData.push([]); // 空行

            // Do阶段
            excelData.push(['Do (执行阶段)']);
            excelData.push(['实施步骤', data.pdcaData.do.implementationSteps.join('; ')]);
            excelData.push(['实际行动', data.pdcaData.do.actualActions]);
            excelData.push(['时间安排', data.pdcaData.do.timeline]);
            excelData.push(['负责人', data.pdcaData.do.responsible]);
            excelData.push(['使用资源', data.pdcaData.do.resourcesUsed]);
            excelData.push(['遇到挑战', data.pdcaData.do.challenges]);
            excelData.push([]); // 空行

            // Check阶段
            excelData.push(['Check (检查阶段)']);
            excelData.push(['监控方法', data.pdcaData.check.monitoringMethod]);
            excelData.push(['数据收集', data.pdcaData.check.dataCollection]);
            excelData.push(['检查结果', data.pdcaData.check.results]);
            excelData.push(['结果分析', data.pdcaData.check.analysis]);
            excelData.push(['时间安排', data.pdcaData.check.timeline]);
            excelData.push(['负责人', data.pdcaData.check.responsible]);
            excelData.push(['偏差情况', data.pdcaData.check.deviations]);
            excelData.push([]); // 空行

            // Act阶段
            excelData.push(['Act (行动阶段)']);
            excelData.push(['标准化措施', data.pdcaData.act.standardization]);
            excelData.push(['改进措施', data.pdcaData.act.improvements]);
            excelData.push(['下一循环', data.pdcaData.act.nextCycle]);
            excelData.push(['经验教训', data.pdcaData.act.lessons]);
            excelData.push(['时间安排', data.pdcaData.act.timeline]);
            excelData.push(['负责人', data.pdcaData.act.responsible]);
            excelData.push(['后续跟进', data.pdcaData.act.followUp]);
            excelData.push([]); // 空行

            // 添加元数据
            excelData.push(['元数据信息']);
            excelData.push(['分析日期', data.metadata.analysisDate]);
            excelData.push(['分析师', data.metadata.analyst]);
            excelData.push(['审核日期', data.metadata.reviewDate]);
            excelData.push(['审核人', data.metadata.reviewer]);
            excelData.push(['版本', data.metadata.version]);
            excelData.push(['状态', data.metadata.status]);

            return excelData;
        }

        function showResult(message, isError) {
            const resultDiv = document.getElementById('exportResult');
            resultDiv.innerHTML = `<div class="result ${isError ? 'error' : ''}">${message}</div>`;
        }
    </script>
</body>
</html>
