package com.elkj.nms.module.evt.dal.dataobject.analyze;

import com.baomidou.mybatisplus.annotation.*;
import com.elkj.nms.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 分析工具数据 DO
 *
 * <AUTHOR>
 */
@TableName("evt_analyze_tool_data")
@KeySequence("evt_analyze_tool_data_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnalyzeToolDataDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 事件ID
     */
    @TableField("event_id")
    private String eventId;

    /**
     * 分析任务ID
     */
    @TableField("assignment_id")
    private String analysisId;

    /**
     * 工具类型：fishbone,five_why,pdca,fmea,rca
     */
    @TableField("tool_type")
    private String toolType;

    /**
     * 工具名称
     */
    @TableField("tool_name")
    private String toolName;

    /**
     * 工具数据(JSON格式)
     */
    @TableField("data_content")
    private String toolData;
    
    /**
     * 主要问题描述
     */
    @TableField("main_problem")
    private String mainProblem;

    /**
     * 分析结论
     */
    @TableField("conclusion")
    private String conclusion;

    /**
     * 创建人ID
     */
    @TableField("creator_user_id")
    private Long creatorUserId;

    /**
     * 版本号
     */
    @TableField("version")
    private Integer versionNo;

    /**
     * 是否有效：0-无效 1-有效
     */
    @TableField("status")
    private String isActive;

    /**
     * 创建者
     */
    @TableField("creator")
    private String creator;

    /**
     * 更新者
     */
    @TableField("updater")
    private String updater;
} 